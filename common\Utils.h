#pragma once
#include "Common.h"

#define BOT_ID_LEN 35

void     GetBotId(char *botId) noexcept;
void     Obfuscate(BYTE *buffer, DWORD bufferSize, const char *key) noexcept;
char    *Utf16toUtf8(wchar_t *utf16);
wchar_t *Utf8toUtf16(const char *utf8);
char    *UnEnc(const char *enc, const char *key, DWORD encLen);

BOOL     VerifyPe(BYTE *pe, DWORD peSize);
BOOL     IsProcessX64(HANDLE hProcess);
void    *Alloc(size_t size);
void    *AllocZ(size_t size);
void    *ReAlloc(void *mem, size_t size);
DWORD    GetPidExplorer();

void     CopyDir(char *from, char *to);