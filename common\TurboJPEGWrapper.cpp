#include "TurboJPEGWrapper.h"
#include "SimpleLogger.h"
#include <malloc.h>

// Static member initialization
BOOL TurboJPEGWrapper::s_globalInitialized = FALSE;
LONG TurboJPEGWrapper::s_instanceCount = 0;

// Global instance for C-style functions
static TurboJPEGWrapper* g_globalWrapper = nullptr;

TurboJPEGWrapper::TurboJPEGWrapper()
    : m_compressor(nullptr)
    , m_decompressor(nullptr)
    , m_initialized(FALSE)
{
    InitializeCriticalSection(&m_criticalSection);
    memset(&m_stats, 0, sizeof(m_stats));
    InterlockedIncrement(&s_instanceCount);
}

TurboJPEGWrapper::~TurboJPEGWrapper()
{
    Cleanup();
    DeleteCriticalSection(&m_criticalSection);
    InterlockedDecrement(&s_instanceCount);
}

BOOL TurboJPEGWrapper::Initialize()
{
    EnterCriticalSection(&m_criticalSection);
    
    if (m_initialized) {
        LeaveCriticalSection(&m_criticalSection);
        return TRUE;
    }

    BOOL success = InitializeCompressor();
    if (success) {
        m_initialized = TRUE;
        s_globalInitialized = TRUE;
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, 
            "TurboJPEG wrapper initialized successfully");
    } else {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, 
            "Failed to initialize TurboJPEG wrapper");
    }

    LeaveCriticalSection(&m_criticalSection);
    return success;
}

void TurboJPEGWrapper::Cleanup()
{
    EnterCriticalSection(&m_criticalSection);
    
    if (m_initialized) {
        CleanupCompressor();
        m_initialized = FALSE;
        
        if (s_instanceCount == 1) {
            s_globalInitialized = FALSE;
        }
        
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, 
            "TurboJPEG wrapper cleaned up");
    }
    
    LeaveCriticalSection(&m_criticalSection);
}

BOOL TurboJPEGWrapper::InitializeCompressor()
{
    // Create TurboJPEG compressor handle
    m_compressor = tjInitCompress();
    if (!m_compressor) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, 
            "Failed to create TurboJPEG compressor: %s", tjGetErrorStr());
        return FALSE;
    }

    // Create decompressor for potential future use
    m_decompressor = tjInitDecompress();
    if (!m_decompressor) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning, 
            "Failed to create TurboJPEG decompressor: %s", tjGetErrorStr());
        // Continue without decompressor - not critical for compression
    }

    return TRUE;
}

void TurboJPEGWrapper::CleanupCompressor()
{
    if (m_compressor) {
        tjDestroy(m_compressor);
        m_compressor = nullptr;
    }
    
    if (m_decompressor) {
        tjDestroy(m_decompressor);
        m_decompressor = nullptr;
    }
}

BOOL TurboJPEGWrapper::CompressBitmap(
    BYTE* bitmapData,
    int width,
    int height,
    int pixelFormat,
    int quality,
    BYTE** jpegBuffer,
    unsigned long* jpegSize)
{
    if (!m_initialized || !bitmapData || !jpegBuffer || !jpegSize) {
        return FALSE;
    }

    EnterCriticalSection(&m_criticalSection);
    
    DWORD startTime = GetTickCount();
    BOOL success = FALSE;
    
    // Calculate input size
    int pixelSize = tjPixelSize[pixelFormat];
    DWORD inputSize = width * height * pixelSize;
    
    // Compress the image
    int result = tjCompress2(
        m_compressor,           // Compressor handle
        bitmapData,             // Source image buffer
        width,                  // Image width
        0,                      // Pitch (0 = width * pixel size)
        height,                 // Image height
        pixelFormat,            // Pixel format
        jpegBuffer,             // Compressed image buffer (allocated by TurboJPEG)
        jpegSize,               // Size of compressed image
        TJSAMP_420,             // Chrominance subsampling (4:2:0 for best compression)
        quality,                // JPEG quality (1-100)
        TJFLAG_FASTDCT          // Use fast DCT algorithm
    );
    
    if (result == 0) {
        success = TRUE;
        DWORD compressionTime = GetTickCount() - startTime;
        UpdateStats(inputSize, *jpegSize, compressionTime);
        
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Debug, 
            "TurboJPEG compression: %dx%d, %d bytes -> %lu bytes (%.1f%% ratio) in %dms",
            width, height, inputSize, *jpegSize, 
            (double)*jpegSize / inputSize * 100.0, compressionTime);
    } else {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, 
            "TurboJPEG compression failed: %s", tjGetErrorStr());
    }
    
    LeaveCriticalSection(&m_criticalSection);
    return success;
}

BOOL TurboJPEGWrapper::CompressFromHBITMAP(
    HDC hdc,
    HBITMAP hBitmap,
    int width,
    int height,
    int quality,
    BYTE** jpegBuffer,
    unsigned long* jpegSize)
{
    if (!m_initialized || !hdc || !hBitmap || !jpegBuffer || !jpegSize) {
        return FALSE;
    }

    BYTE* bitmapData = nullptr;
    int pixelFormat = TJPF_BGR; // Windows default
    BOOL success = FALSE;

    // Extract bitmap data
    if (ExtractBitmapData(hdc, hBitmap, width, height, &bitmapData, &pixelFormat)) {
        // Compress the extracted data
        success = CompressBitmap(bitmapData, width, height, pixelFormat, 
                               quality, jpegBuffer, jpegSize);
        
        // Free the extracted bitmap data
        FreeBitmapData(bitmapData);
    }

    return success;
}

BOOL TurboJPEGWrapper::CompressBitmapToBuffer(
    BYTE* bitmapData,
    int width,
    int height,
    int pixelFormat,
    int quality,
    BYTE* jpegBuffer,
    unsigned long bufferSize,
    unsigned long* jpegSize)
{
    if (!m_initialized || !bitmapData || !jpegBuffer || !jpegSize) {
        return FALSE;
    }

    EnterCriticalSection(&m_criticalSection);

    DWORD startTime = GetTickCount();
    BOOL success = FALSE;

    // Calculate input size
    int pixelSize = tjPixelSize[pixelFormat];
    DWORD inputSize = width * height * pixelSize;

    // Use a temporary pointer for TurboJPEG
    BYTE* tempBuffer = jpegBuffer;
    unsigned long tempSize = bufferSize;

    // Compress the image
    int result = tjCompress2(
        m_compressor,           // Compressor handle
        bitmapData,             // Source image buffer
        width,                  // Image width
        0,                      // Pitch (0 = width * pixel size)
        height,                 // Image height
        pixelFormat,            // Pixel format
        &tempBuffer,            // Compressed image buffer
        &tempSize,              // Size of compressed image
        TJSAMP_420,             // Chrominance subsampling
        quality,                // JPEG quality (1-100)
        TJFLAG_FASTDCT          // Use fast DCT algorithm
    );

    if (result == 0 && tempSize <= bufferSize) {
        *jpegSize = tempSize;
        success = TRUE;

        DWORD compressionTime = GetTickCount() - startTime;
        UpdateStats(inputSize, *jpegSize, compressionTime);
    } else {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "TurboJPEG buffer compression failed: %s", tjGetErrorStr());
    }

    LeaveCriticalSection(&m_criticalSection);
    return success;
}

BOOL TurboJPEGWrapper::ExtractBitmapData(HDC hdc, HBITMAP hBitmap, int width, int height,
                                        BYTE** bitmapData, int* pixelFormat)
{
    // Create a BITMAPINFO structure for 24-bit RGB
    BITMAPINFO bmi = {0};
    bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
    bmi.bmiHeader.biWidth = width;
    bmi.bmiHeader.biHeight = -height; // Negative for top-down DIB
    bmi.bmiHeader.biPlanes = 1;
    bmi.bmiHeader.biBitCount = 24; // 24-bit RGB
    bmi.bmiHeader.biCompression = BI_RGB;
    
    // Calculate the size needed for the bitmap data
    int rowSize = ((width * 3 + 3) & ~3); // Align to 4-byte boundary
    int dataSize = rowSize * height;
    
    // Allocate memory for bitmap data
    *bitmapData = (BYTE*)malloc(dataSize);
    if (!*bitmapData) {
        return FALSE;
    }
    
    // Extract the bitmap data
    int result = GetDIBits(hdc, hBitmap, 0, height, *bitmapData, &bmi, DIB_RGB_COLORS);
    if (result == 0) {
        free(*bitmapData);
        *bitmapData = nullptr;
        return FALSE;
    }
    
    *pixelFormat = TJPF_BGR; // Windows DIBs are BGR format
    return TRUE;
}

void TurboJPEGWrapper::FreeBitmapData(BYTE* bitmapData)
{
    if (bitmapData) {
        free(bitmapData);
    }
}

void TurboJPEGWrapper::UpdateStats(DWORD inputSize, DWORD outputSize, DWORD compressionTime)
{
    m_stats.totalCompressions++;
    m_stats.totalInputBytes += inputSize;
    m_stats.totalOutputBytes += outputSize;
    m_stats.totalCompressionTime += compressionTime;
    
    // Calculate running averages
    m_stats.averageCompressionRatio = (double)m_stats.totalOutputBytes / m_stats.totalInputBytes;
    m_stats.averageCompressionTime = (double)m_stats.totalCompressionTime / m_stats.totalCompressions;
}

void TurboJPEGWrapper::ResetStats()
{
    EnterCriticalSection(&m_criticalSection);
    memset(&m_stats, 0, sizeof(m_stats));
    LeaveCriticalSection(&m_criticalSection);
}

int TurboJPEGWrapper::GetOptimalPixelFormat()
{
    // BGR is optimal for Windows bitmaps
    return TJPF_BGR;
}

unsigned long TurboJPEGWrapper::GetMaxCompressedSize(int width, int height)
{
    // TurboJPEG provides this calculation
    return tjBufSize(width, height, TJSAMP_420);
}

// Global C-style functions for easy integration
extern "C" {

BOOL InitializeTurboJPEG()
{
    if (!g_globalWrapper) {
        g_globalWrapper = new TurboJPEGWrapper();
        if (g_globalWrapper && g_globalWrapper->Initialize()) {
            return TRUE;
        } else {
            delete g_globalWrapper;
            g_globalWrapper = nullptr;
            return FALSE;
        }
    }
    return TRUE;
}

void CleanupTurboJPEG()
{
    if (g_globalWrapper) {
        delete g_globalWrapper;
        g_globalWrapper = nullptr;
    }
}

BOOL TurboJPEG_CompressBitmap(
    HDC hdc,
    HBITMAP hBitmap,
    int width,
    int height,
    int quality,
    BYTE** jpegBuffer,
    unsigned long* jpegSize)
{
    if (!g_globalWrapper) {
        return FALSE;
    }
    
    return g_globalWrapper->CompressFromHBITMAP(hdc, hBitmap, width, height, 
                                               quality, jpegBuffer, jpegSize);
}

void TurboJPEG_FreeBuffer(BYTE* buffer)
{
    if (buffer) {
        tjFree(buffer); // Use TurboJPEG's free function
    }
}

} // extern "C"
