#include "DifferentialCapture.h"
#include <memory.h>
#include <stdlib.h>
#include <algorithm>

// Initialize region detection state
BOOL InitializeRegionDetection(RegionDetectionState* state, DWORD width, DWORD height, DWORD bytesPerPixel)
{
    if (!state || width == 0 || height == 0 || bytesPerPixel == 0) {
        return FALSE;
    }

    // Cleanup any existing state
    CleanupRegionDetection(state);

    DWORD frameSize = width * height * bytesPerPixel;
    
    state->previousFrame = (BYTE*)malloc(frameSize);
    state->currentFrame = (BYTE*)malloc(frameSize);
    
    if (!state->previousFrame || !state->currentFrame) {
        CleanupRegionDetection(state);
        return FALSE;
    }

    // Initialize with zeros
    memset(state->previousFrame, 0, frameSize);
    memset(state->currentFrame, 0, frameSize);

    state->frameWidth = width;
    state->frameHeight = height;
    state->bytesPerPixel = bytesPerPixel;
    state->changeThreshold = 15; // Default threshold for pixel change detection (increased for better performance)
    state->initialized = TRUE;

    return TRUE;
}

// Cleanup region detection state
void CleanupRegionDetection(RegionDetectionState* state)
{
    if (!state) return;

    if (state->previousFrame) {
        free(state->previousFrame);
        state->previousFrame = nullptr;
    }
    
    if (state->currentFrame) {
        free(state->currentFrame);
        state->currentFrame = nullptr;
    }

    state->initialized = FALSE;
}

// Helper function to calculate pixel difference
static DWORD CalculatePixelDifference(const BYTE* pixel1, const BYTE* pixel2, DWORD bytesPerPixel)
{
    DWORD totalDiff = 0;
    for (DWORD i = 0; i < bytesPerPixel; i++) {
        int diff = abs((int)pixel1[i] - (int)pixel2[i]);
        totalDiff += diff;
    }
    return totalDiff;
}

// Helper function to check if a region overlaps or is adjacent to another
static BOOL RegionsOverlapOrAdjacent(const DirtyRegion* region1, const DirtyRegion* region2, DWORD maxDistance)
{
    // Check if regions are within merge distance
    LONG left1 = (LONG)region1->x - maxDistance;
    LONG top1 = (LONG)region1->y - maxDistance;
    LONG right1 = (LONG)(region1->x + region1->width) + maxDistance;
    LONG bottom1 = (LONG)(region1->y + region1->height) + maxDistance;

    LONG left2 = (LONG)region2->x;
    LONG top2 = (LONG)region2->y;
    LONG right2 = (LONG)(region2->x + region2->width);
    LONG bottom2 = (LONG)(region2->y + region2->height);

    return !(right2 < left1 || left2 > right1 || bottom2 < top1 || top2 > bottom1);
}

// Detect changed regions between current and previous frame
DWORD DetectChangedRegions(RegionDetectionState* state, BYTE* currentFrame, DirtyRegion* regions, DWORD maxRegions)
{
    if (!state || !state->initialized || !currentFrame || !regions || maxRegions == 0) {
        return 0;
    }

    // Copy current frame to state buffer
    DWORD frameSize = state->frameWidth * state->frameHeight * state->bytesPerPixel;
    memcpy(state->currentFrame, currentFrame, frameSize);

    DWORD regionCount = 0;
    DWORD blockSize = MIN_REGION_SIZE; // Start with minimum block size

    // Scan the frame in blocks to detect changes
    for (DWORD y = 0; y < state->frameHeight && regionCount < maxRegions; y += blockSize) {
        for (DWORD x = 0; x < state->frameWidth && regionCount < maxRegions; x += blockSize) {
            
            DWORD blockWidth = min(blockSize, state->frameWidth - x);
            DWORD blockHeight = min(blockSize, state->frameHeight - y);
            
            BOOL hasChanges = FALSE;
            
            // Check if this block has changes
            for (DWORD by = 0; by < blockHeight && !hasChanges; by++) {
                for (DWORD bx = 0; bx < blockWidth && !hasChanges; bx++) {
                    DWORD pixelIndex = ((y + by) * state->frameWidth + (x + bx)) * state->bytesPerPixel;
                    
                    DWORD diff = CalculatePixelDifference(
                        &state->currentFrame[pixelIndex],
                        &state->previousFrame[pixelIndex],
                        state->bytesPerPixel
                    );
                    
                    if (diff > state->changeThreshold) {
                        hasChanges = TRUE;
                    }
                }
            }
            
            if (hasChanges) {
                // Expand the region to include all adjacent changed pixels
                DWORD regionLeft = x;
                DWORD regionTop = y;
                DWORD regionRight = x + blockWidth;
                DWORD regionBottom = y + blockHeight;
                
                // Expand right
                BOOL canExpandRight = TRUE;
                while (canExpandRight && regionRight < state->frameWidth) {
                    canExpandRight = FALSE;
                    for (DWORD ey = regionTop; ey < regionBottom; ey++) {
                        DWORD pixelIndex = (ey * state->frameWidth + regionRight) * state->bytesPerPixel;
                        DWORD diff = CalculatePixelDifference(
                            &state->currentFrame[pixelIndex],
                            &state->previousFrame[pixelIndex],
                            state->bytesPerPixel
                        );
                        if (diff > state->changeThreshold) {
                            canExpandRight = TRUE;
                            break;
                        }
                    }
                    if (canExpandRight) regionRight++;
                }
                
                // Expand down
                BOOL canExpandDown = TRUE;
                while (canExpandDown && regionBottom < state->frameHeight) {
                    canExpandDown = FALSE;
                    for (DWORD ex = regionLeft; ex < regionRight; ex++) {
                        DWORD pixelIndex = (regionBottom * state->frameWidth + ex) * state->bytesPerPixel;
                        DWORD diff = CalculatePixelDifference(
                            &state->currentFrame[pixelIndex],
                            &state->previousFrame[pixelIndex],
                            state->bytesPerPixel
                        );
                        if (diff > state->changeThreshold) {
                            canExpandDown = TRUE;
                            break;
                        }
                    }
                    if (canExpandDown) regionBottom++;
                }
                
                // Create the region
                regions[regionCount].x = regionLeft;
                regions[regionCount].y = regionTop;
                regions[regionCount].width = regionRight - regionLeft;
                regions[regionCount].height = regionBottom - regionTop;
                regions[regionCount].dataSize = regions[regionCount].width * regions[regionCount].height * state->bytesPerPixel;
                
                regionCount++;
                
                // Skip the area we just processed
                x = regionRight - blockSize; // -blockSize because the loop will add blockSize
            }
        }
    }

    // Update previous frame
    memcpy(state->previousFrame, state->currentFrame, frameSize);

    return regionCount;
}

// Merge adjacent regions to reduce the number of regions
DWORD MergeAdjacentRegions(DirtyRegion* regions, DWORD regionCount, DWORD maxDistance)
{
    if (!regions || regionCount <= 1) {
        return regionCount;
    }

    BOOL merged = TRUE;
    while (merged && regionCount > 1) {
        merged = FALSE;
        
        for (DWORD i = 0; i < regionCount - 1 && !merged; i++) {
            for (DWORD j = i + 1; j < regionCount && !merged; j++) {
                if (RegionsOverlapOrAdjacent(&regions[i], &regions[j], maxDistance)) {
                    // Merge regions j into i
                    DWORD newLeft = min(regions[i].x, regions[j].x);
                    DWORD newTop = min(regions[i].y, regions[j].y);
                    DWORD newRight = max(regions[i].x + regions[i].width, regions[j].x + regions[j].width);
                    DWORD newBottom = max(regions[i].y + regions[i].height, regions[j].y + regions[j].height);
                    
                    regions[i].x = newLeft;
                    regions[i].y = newTop;
                    regions[i].width = newRight - newLeft;
                    regions[i].height = newBottom - newTop;
                    regions[i].dataSize = regions[i].width * regions[i].height * 3; // Assuming 3 bytes per pixel
                    
                    // Remove region j by moving the last region to position j
                    if (j < regionCount - 1) {
                        regions[j] = regions[regionCount - 1];
                    }
                    regionCount--;
                    merged = TRUE;
                }
            }
        }
    }

    return regionCount;
}

// Extract pixel data for a specific region
BOOL ExtractRegionData(BYTE* frameBuffer, DWORD frameWidth, DWORD frameHeight, 
                      const DirtyRegion* region, BYTE* regionData)
{
    if (!frameBuffer || !region || !regionData) {
        return FALSE;
    }

    if (region->x + region->width > frameWidth || region->y + region->height > frameHeight) {
        return FALSE;
    }

    DWORD bytesPerPixel = 3; // Assuming RGB
    DWORD regionDataIndex = 0;

    for (DWORD y = 0; y < region->height; y++) {
        DWORD frameRowStart = ((region->y + y) * frameWidth + region->x) * bytesPerPixel;
        DWORD regionRowSize = region->width * bytesPerPixel;
        
        memcpy(&regionData[regionDataIndex], &frameBuffer[frameRowStart], regionRowSize);
        regionDataIndex += regionRowSize;
    }

    return TRUE;
}

// Apply region data to a frame buffer
BOOL ApplyRegionData(BYTE* frameBuffer, DWORD frameWidth, DWORD frameHeight,
                    const DirtyRegion* region, const BYTE* regionData)
{
    if (!frameBuffer || !region || !regionData) {
        return FALSE;
    }

    if (region->x + region->width > frameWidth || region->y + region->height > frameHeight) {
        return FALSE;
    }

    DWORD bytesPerPixel = 3; // Assuming RGB
    DWORD regionDataIndex = 0;

    for (DWORD y = 0; y < region->height; y++) {
        DWORD frameRowStart = ((region->y + y) * frameWidth + region->x) * bytesPerPixel;
        DWORD regionRowSize = region->width * bytesPerPixel;
        
        memcpy(&frameBuffer[frameRowStart], &regionData[regionDataIndex], regionRowSize);
        regionDataIndex += regionRowSize;
    }

    return TRUE;
}

// Adjust change threshold based on performance metrics
void AdjustChangeThreshold(RegionDetectionState* state, DWORD regionCount, DWORD targetRegionCount)
{
    if (!state || !state->initialized) return;

    // If we have too many regions, increase threshold to reduce sensitivity
    if (regionCount > targetRegionCount * 2) {
        state->changeThreshold = min(state->changeThreshold + 5, 50);
    }
    // If we have too few regions, decrease threshold to increase sensitivity
    else if (regionCount < targetRegionCount / 2 && regionCount > 0) {
        state->changeThreshold = max(state->changeThreshold - 2, 5);
    }
}

// Optimize regions by removing small isolated regions
DWORD OptimizeRegions(DirtyRegion* regions, DWORD regionCount, DWORD minRegionSize)
{
    if (!regions || regionCount == 0) return 0;

    DWORD optimizedCount = 0;

    for (DWORD i = 0; i < regionCount; i++) {
        // Keep regions that are large enough
        if (regions[i].width >= minRegionSize && regions[i].height >= minRegionSize) {
            if (optimizedCount != i) {
                regions[optimizedCount] = regions[i];
            }
            optimizedCount++;
        }
    }

    return optimizedCount;
}
