#pragma once
#include <windows.h>
#include <string>

namespace ModernHVNC {

enum class LogLevel : int {
    Info = 0,
    Warning = 1,
    Error = 2,
    Debug = 3
};

class SimpleLogger {
private:
    static bool initialized_;
    static HANDLE log_file_;
    static constexpr size_t MAX_LOG_SIZE = 1024;
    
public:
    static void Initialize(const char* log_path = "hvnc_client.log") noexcept;
    static void Log(LogLevel level, const char* format, ...) noexcept;
    static void LogAppStart(const char* app_name, const char* path = nullptr) noexcept;
    static void LogAppEnd(const char* app_name, DWORD exit_code = 0) noexcept;
    static void Shutdown() noexcept;
    
private:
    static const char* GetLogLevelString(LogLevel level) noexcept;
    static std::string GetTimestamp() noexcept;
};

} // namespace ModernHVNC
