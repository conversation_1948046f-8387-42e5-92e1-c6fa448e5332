#include "ModernUtils.h"
#include <stdarg.h>
#include <vector>

namespace ModernHVNC {

// Modern string utilities implementation
std::string Utf16toUtf8String(const wchar_t* utf16) {
    if (!utf16) return {};
    
    const int size = WideCharToMultiByte(CP_UTF8, 0, utf16, -1, nullptr, 0, nullptr, nullptr);
    if (size <= 0) return {};
    
    std::vector<char> buffer(size);
    WideCharToMultiByte(CP_UTF8, 0, utf16, -1, buffer.data(), size, nullptr, nullptr);
    
    return std::string(buffer.data());
}

std::wstring Utf8toUtf16String(const char* utf8) {
    if (!utf8) return {};
    
    const int size = MultiByteToWideChar(CP_UTF8, 0, utf8, -1, nullptr, 0);
    if (size <= 0) return {};
    
    std::vector<wchar_t> buffer(size);
    MultiByteToWideChar(CP_UTF8, 0, utf8, -1, buffer.data(), size);
    
    return std::wstring(buffer.data());
}

std::string FormatString(const char* format, ...) {
    va_list args;
    va_start(args, format);
    
    const int size = _vscprintf(format, args) + 1;
    std::vector<char> buffer(size);
    
    _vsnprintf_s(buffer.data(), size, _TRUNCATE, format, args);
    va_end(args);
    
    return std::string(buffer.data());
}

std::wstring FormatWString(const wchar_t* format, ...) {
    va_list args;
    va_start(args, format);
    
    const int size = _vscwprintf(format, args) + 1;
    std::vector<wchar_t> buffer(size);
    
    _vsnwprintf_s(buffer.data(), size, _TRUNCATE, format, args);
    va_end(args);
    
    return std::wstring(buffer.data());
}

// Structured binding utilities implementation
std::pair<DWORD, DWORD> GetFileSize(HANDLE hFile) noexcept {
    try {
        LARGE_INTEGER size;
        if (GetFileSizeEx(hFile, &size)) {
            return {size.LowPart, size.HighPart};
        }
    } catch (...) {
        // Fall through to error case
    }
    return {0, 0};
}

std::tuple<bool, DWORD, DWORD> GetWindowDimensions(HWND hWnd) noexcept {
    try {
        RECT rect;
        if (GetWindowRect(hWnd, &rect)) {
            const DWORD width = static_cast<DWORD>(rect.right - rect.left);
            const DWORD height = static_cast<DWORD>(rect.bottom - rect.top);
            return {true, width, height};
        }
    } catch (...) {
        // Fall through to error case
    }
    return {false, 0, 0};
}

} // namespace ModernHVNC
