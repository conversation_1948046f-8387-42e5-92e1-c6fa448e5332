#pragma once

// ============================================================================
// HVNC PERFORMANCE CONFIGURATION
// ============================================================================
// This file contains all performance-related settings for the HVNC system.
// Modify the values below to customize performance characteristics.

// ============================================================================
// MAIN PERFORMANCE SETTINGS - MODIFY THESE VALUES AS NEEDED
// ============================================================================

// JPEG compression quality (1-100)
// - Higher values = better quality, slower compression, larger files
// - Lower values = faster compression, lower quality, smaller files
// RECOMMENDED: 90 (High Quality), 65 (Balanced), 35 (High Performance)
#define HVNC_JPEG_QUALITY 90

// Frame rate limit (frames per second)
// - Higher values = smoother video, more CPU usage, more bandwidth
// - Lower values = less smooth video, less CPU usage, less bandwidth
// RECOMMENDED: 30 (High Quality), 45 (Balanced), 60 (High Performance)
#define HVNC_FRAME_RATE_LIMIT 30

// Differential capture threshold (0-255)
// - Lower values = more sensitive to changes, more updates sent
// - Higher values = less sensitive to changes, fewer updates sent
// RECOMMENDED: 25 (High Quality), 20 (Balanced), 15 (High Performance)
#define HVNC_PIXEL_DIFF_THRESHOLD 25

// LZNT1 compression level (1-12)
// - Higher values = better compression, slower processing
// - Lower values = faster processing, less compression
// RECOMMENDED: 8 (High Quality), 6 (Balanced), 4 (High Performance)
#define HVNC_COMPRESSION_LEVEL 8

// ============================================================================
// CALCULATED VALUES - DO NOT MODIFY THESE
// ============================================================================

// Capture interval in milliseconds (calculated from frame rate)
#define HVNC_CAPTURE_INTERVAL_MS (1000 / HVNC_FRAME_RATE_LIMIT)

// Profile information for logging
#define HVNC_PROFILE_NAME "High Quality"
#define HVNC_PROFILE_DESCRIPTION "Design/Quality optimized"

// Logging macro for profile information
#define HVNC_LOG_PROFILE_INFO() \
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, \
        "HVNC Profile: %s | JPEG Quality: %d | Frame Rate: %d FPS | Compression: %d", \
        HVNC_PROFILE_NAME, HVNC_JPEG_QUALITY, HVNC_FRAME_RATE_LIMIT, HVNC_COMPRESSION_LEVEL)
