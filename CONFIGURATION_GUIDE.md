
## How to Modify Performance Settings

### **File to Edit: `common/CompileTimeConfig.h`**

This file contains all the performance settings with clear comments. Simply modify the values and rebuild.

### **Key Settings to Modify:**

#### 1. **JPEG Quality** (Line 17)
```cpp
#define HVNC_JPEG_QUALITY 90
```
- **Range:** 1-100
- **90** = High Quality (current default)
- **65** = Balanced
- **35** = High Performance

#### 2. **Frame Rate** (Line 23)
```cpp
#define HVNC_FRAME_RATE_LIMIT 30
```
- **Range:** 1-120 FPS
- **30** = High Quality (current default)
- **45** = Balanced
- **60** = High Performance

#### 3. **Differential Capture Threshold** (Line 29)
```cpp
#define HVNC_PIXEL_DIFF_THRESHOLD 25
```
- **Range:** 0-255
- **25** = High Quality (current default)
- **20** = Balanced
- **15** = High Performance

#### 4. **Compression Level** (Line 35)
```cpp
#define HVNC_COMPRESSION_LEVEL 8
```
- **Range:** 1-12
- **8** = High Quality (current default)
- **6** = Balanced
- **4** = High Performance

## Quick Configuration Presets

### **High Performance Gaming**
```cpp
#define HVNC_JPEG_QUALITY 35
#define HVNC_FRAME_RATE_LIMIT 60
#define HVNC_PIXEL_DIFF_THRESHOLD 15
#define HVNC_COMPRESSION_LEVEL 4
```

### **Balanced General Use**
```cpp
#define HVNC_JPEG_QUALITY 65
#define HVNC_FRAME_RATE_LIMIT 45
#define HVNC_PIXEL_DIFF_THRESHOLD 20
#define HVNC_COMPRESSION_LEVEL 6
```

### **High Quality Design Work** (Current Default)
```cpp
#define HVNC_JPEG_QUALITY 90
#define HVNC_FRAME_RATE_LIMIT 30
#define HVNC_PIXEL_DIFF_THRESHOLD 25
#define HVNC_COMPRESSION_LEVEL 8
```

## How to Build

1. **Edit Settings:** Modify values in `common/CompileTimeConfig.h`
2. **Run Build:** Execute `build.bat`
3. **Output:** Find executables in:
   - Client: `Client\_bin\Release\Win32\Client.exe`
   - Server: `Server\_bin\Release\Win32\Server.exe`

## Notes

- **DO NOT** modify the calculated values section in CompileTimeConfig.h
- Only modify the "MAIN PERFORMANCE SETTINGS" section
- Rebuild after any configuration changes
- Current default is optimized for High Quality (design work)
- All settings are compile-time for maximum performance
