#pragma once

#include <Windows.h>
#include "turbojpeg.h"
#include "CompileTimeConfig.h"

// TurboJPEG wrapper class for high-performance JPEG compression
class TurboJPEGWrapper
{
public:
    // Constructor/Destructor
    TurboJPEGWrapper();
    ~TurboJPEGWrapper();

    // Initialization and cleanup
    BOOL Initialize();
    void Cleanup();

    // Main compression function - converts RGB/BGR bitmap data to JPEG
    BOOL CompressBitmap(
        BYTE* bitmapData,           // Input bitmap data (RGB or BGR)
        int width,                  // Image width
        int height,                 // Image height
        int pixelFormat,            // TJPF_RGB, TJPF_BGR, TJPF_RGBX, etc.
        int quality,                // JPEG quality (1-100)
        BYTE** jpegBuffer,          // Output: compressed JPEG data (caller must free)
        unsigned long* jpegSize     // Output: size of compressed data
    );

    // Compress from Windows HBITMAP
    BOOL CompressFromHBITMAP(
        HDC hdc,                    // Device context
        HBITMAP hBitmap,            // Source bitmap
        int width,                  // Image width
        int height,                 // Image height
        int quality,                // JPEG quality (1-100)
        BYTE** jpegBuffer,          // Output: compressed JPEG data
        unsigned long* jpegSize     // Output: size of compressed data
    );

    // Compress bitmap data directly to existing buffer
    BOOL CompressBitmapToBuffer(
        BYTE* bitmapData,           // Input bitmap data
        int width,                  // Image width
        int height,                 // Image height
        int pixelFormat,            // Pixel format
        int quality,                // JPEG quality (1-100)
        BYTE* jpegBuffer,           // Pre-allocated output buffer
        unsigned long bufferSize,   // Size of output buffer
        unsigned long* jpegSize     // Output: actual compressed size
    );

    // Utility functions
    static int GetOptimalPixelFormat();
    static unsigned long GetMaxCompressedSize(int width, int height);
    static BOOL IsInitialized() { return s_globalInitialized; }

    // Performance monitoring
    struct CompressionStats {
        DWORD totalCompressions;
        DWORD totalInputBytes;
        DWORD totalOutputBytes;
        DWORD totalCompressionTime;
        double averageCompressionRatio;
        double averageCompressionTime;
    };

    const CompressionStats& GetStats() const { return m_stats; }
    void ResetStats();

private:
    // TurboJPEG handles
    tjhandle m_compressor;
    tjhandle m_decompressor;

    // Thread safety
    CRITICAL_SECTION m_criticalSection;
    BOOL m_initialized;

    // Global initialization tracking
    static BOOL s_globalInitialized;
    static LONG s_instanceCount;

    // Performance statistics
    CompressionStats m_stats;

    // Internal helper functions
    BOOL InitializeCompressor();
    void CleanupCompressor();
    void UpdateStats(DWORD inputSize, DWORD outputSize, DWORD compressionTime);
    
    // Bitmap data extraction helpers
    BOOL ExtractBitmapData(HDC hdc, HBITMAP hBitmap, int width, int height, 
                          BYTE** bitmapData, int* pixelFormat);
    void FreeBitmapData(BYTE* bitmapData);
};

// Global functions for easy integration
extern "C" {
    // Initialize global TurboJPEG system
    BOOL InitializeTurboJPEG();
    
    // Cleanup global TurboJPEG system
    void CleanupTurboJPEG();
    
    // Quick compression function for compatibility with existing code
    BOOL TurboJPEG_CompressBitmap(
        HDC hdc,
        HBITMAP hBitmap,
        int width,
        int height,
        int quality,
        BYTE** jpegBuffer,
        unsigned long* jpegSize
    );
    
    // Free JPEG buffer allocated by TurboJPEG
    void TurboJPEG_FreeBuffer(BYTE* buffer);
}

// Pixel format constants for TurboJPEG
#ifndef TJPF_RGB
#define TJPF_RGB        0   // RGB pixel format
#define TJPF_BGR        1   // BGR pixel format (Windows default)
#define TJPF_RGBX       2   // RGBX pixel format
#define TJPF_BGRX       3   // BGRX pixel format
#define TJPF_XBGR       4   // XBGR pixel format
#define TJPF_XRGB       5   // XRGB pixel format
#define TJPF_GRAY       6   // Grayscale pixel format
#define TJPF_RGBA       7   // RGBA pixel format
#define TJPF_BGRA       8   // BGRA pixel format
#define TJPF_ABGR       9   // ABGR pixel format
#define TJPF_ARGB       10  // ARGB pixel format
#define TJPF_CMYK       11  // CMYK pixel format
#endif

/*
 * REMOVED: Runtime quality presets
 * Quality is now determined at compile time via CompileTimeConfig.h
 * Use HVNC_JPEG_QUALITY constant instead of these presets.
 *
 * Legacy constants kept for compatibility:
 */
#define TURBOJPEG_QUALITY_LOW       30
#define TURBOJPEG_QUALITY_MEDIUM    60
#define TURBOJPEG_QUALITY_HIGH      85
#define TURBOJPEG_QUALITY_MAXIMUM   95

// Compile-time quality - use this instead of runtime parameters
#define TURBOJPEG_COMPILE_TIME_QUALITY  HVNC_JPEG_QUALITY

// Error handling
#define TURBOJPEG_SUCCESS           0
#define TURBOJPEG_ERROR_INIT        -1
#define TURBOJPEG_ERROR_COMPRESS    -2
#define TURBOJPEG_ERROR_MEMORY      -3
#define TURBOJPEG_ERROR_PARAMS      -4
