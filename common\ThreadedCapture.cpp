#include "ThreadedCapture.h"
#include "SimpleLogger.h"
#include "TurboJPEGWrapper.h"
#include <process.h>
#include <algorithm>

// External function declarations
#include "../common/Api.h"

extern "C" {
    NTSTATUS WINAPI RtlGetCompressionWorkSpaceSize(
        USHORT CompressionFormatAndEngine,
        PULONG CompressBufferWorkSpaceSize,
        PULONG CompressFragmentWorkSpaceSize
    );

    NTSTATUS WINAPI RtlCompressBuffer(
        USHORT CompressionFormatAndEngine,
        PUCHAR UncompressedBuffer,
        ULONG UncompressedBufferSize,
        PUC<PERSON><PERSON> CompressedBuffer,
        ULONG CompressedBufferSize,
        ULONG UncompressedChunkSize,
        PULONG FinalCompressedSize,
        PVOID WorkSpace
    );
}

// Global instance
static ThreadedCaptureSystem* g_captureSystem = nullptr;

// Forward declarations for functions we need
BOOL GetDeskPixelsDifferential(int serverWidth, int serverHeight,
                              DifferentialCaptureHeader* header,
                              DirtyRegion* regions, BYTE** regionData);

// Function to get current screen dimensions
void GetCurrentScreenDimensions(int* width, int* height)
{
    RECT rect;
    HWND hWndDesktop = GetDesktopWindow();
    GetWindowRect(hWndDesktop, &rect);
    *width = rect.right;
    *height = rect.bottom;
}

// Constructor is now inline in header

ThreadedCaptureSystem::~ThreadedCaptureSystem()
{
    Stop();

    // Cleanup TurboJPEG
    CleanupTurboJPEG();

    if (m_captureEvent) {
        CloseHandle(m_captureEvent);
        m_captureEvent = nullptr;
    }
    if (m_encodeEvent) {
        CloseHandle(m_encodeEvent);
        m_encodeEvent = nullptr;
    }
    if (m_sendEvent) {
        CloseHandle(m_sendEvent);
        m_sendEvent = nullptr;
    }
}

BOOL ThreadedCaptureSystem::Initialize(SOCKET socket)
{
    if (socket == INVALID_SOCKET) {
        return FALSE;
    }

    m_socket = socket;

    // Initialize TurboJPEG for this thread system
    if (!InitializeTurboJPEG()) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "Failed to initialize TurboJPEG for threaded capture");
        return FALSE;
    }

    // Create synchronization events
    m_captureEvent = CreateEvent(nullptr, FALSE, FALSE, nullptr);
    m_encodeEvent = CreateEvent(nullptr, FALSE, FALSE, nullptr);
    m_sendEvent = CreateEvent(nullptr, FALSE, FALSE, nullptr);

    if (!m_captureEvent || !m_encodeEvent || !m_sendEvent) {
        return FALSE;
    }

    return TRUE;
}

BOOL ThreadedCaptureSystem::Start()
{
    if (ATOMIC_LOAD(&m_running)) {
        return TRUE; // Already running
    }

    ATOMIC_STORE(&m_shutdown, 0);
    ATOMIC_STORE(&m_running, 1);

    // Create threads
    m_captureThread = CreateThread(nullptr, 0, CaptureThreadProc, this, 0, nullptr);
    m_encodeThread = CreateThread(nullptr, 0, EncodeThreadProc, this, 0, nullptr);
    m_sendThread = CreateThread(nullptr, 0, SendThreadProc, this, 0, nullptr);

    if (!m_captureThread || !m_encodeThread || !m_sendThread) {
        Stop();
        return FALSE;
    }

    // Set thread priorities for optimal performance
    SetThreadPriority(m_captureThread, THREAD_PRIORITY_TIME_CRITICAL);
    SetThreadPriority(m_encodeThread, THREAD_PRIORITY_ABOVE_NORMAL);
    SetThreadPriority(m_sendThread, THREAD_PRIORITY_NORMAL);

    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, 
        "Threaded capture system started successfully");

    return TRUE;
}

void ThreadedCaptureSystem::Stop()
{
    if (!ATOMIC_LOAD(&m_running)) {
        return; // Already stopped
    }

    ATOMIC_STORE(&m_shutdown, 1);
    ATOMIC_STORE(&m_running, 0);

    // Signal all threads to wake up and exit
    if (m_captureEvent) SetEvent(m_captureEvent);
    if (m_encodeEvent) SetEvent(m_encodeEvent);
    if (m_sendEvent) SetEvent(m_sendEvent);

    // Wait for threads to finish
    HANDLE threads[] = { m_captureThread, m_encodeThread, m_sendThread };
    WaitForMultipleObjects(3, threads, TRUE, 5000);

    // Close thread handles
    if (m_captureThread) {
        CloseHandle(m_captureThread);
        m_captureThread = nullptr;
    }
    if (m_encodeThread) {
        CloseHandle(m_encodeThread);
        m_encodeThread = nullptr;
    }
    if (m_sendThread) {
        CloseHandle(m_sendThread);
        m_sendThread = nullptr;
    }

    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, 
        "Threaded capture system stopped");
}

ThreadedCaptureStats ThreadedCaptureSystem::GetStats() const
{
    return m_stats;
}

DWORD WINAPI ThreadedCaptureSystem::CaptureThreadProc(LPVOID param)
{
    ThreadedCaptureSystem* system = static_cast<ThreadedCaptureSystem*>(param);
    system->CaptureThreadMain();
    return 0;
}

DWORD WINAPI ThreadedCaptureSystem::EncodeThreadProc(LPVOID param)
{
    ThreadedCaptureSystem* system = static_cast<ThreadedCaptureSystem*>(param);
    system->EncodeThreadMain();
    return 0;
}

DWORD WINAPI ThreadedCaptureSystem::SendThreadProc(LPVOID param)
{
    ThreadedCaptureSystem* system = static_cast<ThreadedCaptureSystem*>(param);
    system->SendThreadMain();
    return 0;
}

void ThreadedCaptureSystem::CaptureThreadMain()
{
    const DWORD targetFrameTime = 16; // ~60 FPS
    DWORD lastCaptureTime = GetTickCount();

    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Debug, 
        "Capture thread started");

    while (!ATOMIC_LOAD(&m_shutdown)) {
        DWORD currentTime = GetTickCount();
        DWORD elapsed = currentTime - lastCaptureTime;

        // Frame rate limiting
        if (elapsed < targetFrameTime) {
            Sleep(targetFrameTime - elapsed);
            continue;
        }

        DWORD captureStartTime = GetTickCount();

        // Create new capture frame
        CaptureFrame frame;
        frame.frameId = ATOMIC_FETCH_ADD(&m_frameCounter, 1);
        frame.timestamp = currentTime;

        // Get current screen dimensions
        int screenWidth, screenHeight;
        GetCurrentScreenDimensions(&screenWidth, &screenHeight);

        // Perform screen capture with differential detection
        BOOL hasData = GetDeskPixelsDifferential(screenWidth, screenHeight,
                                                &frame.header,
                                                frame.regions,
                                                &frame.regionData);

        if (hasData && frame.header.regionCount > 0) {
            frame.regionDataSize = frame.header.totalDataSize;
            frame.isValid = TRUE;

            // Try to push to encode queue
            if (!m_captureQueue.Push(frame)) {
                // Queue is full, drop this frame
                ATOMIC_FETCH_ADD(&m_stats.droppedFrames, 1);
                ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning, 
                    "Dropped frame %d - capture queue full", frame.frameId);
            } else {
                ATOMIC_FETCH_ADD(&m_stats.capturedFrames, 1);
                SetEvent(m_encodeEvent); // Signal encode thread
            }
        }

        // Update timing statistics
        DWORD captureTime = GetTickCount() - captureStartTime;
        ATOMIC_STORE(&m_stats.avgCaptureTime,
            (ATOMIC_LOAD(&m_stats.avgCaptureTime) * 7 + captureTime) / 8); // Moving average

        lastCaptureTime = currentTime;
    }

    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Debug, 
        "Capture thread finished");
}

void ThreadedCaptureSystem::EncodeThreadMain()
{
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Debug, 
        "Encode thread started");

    while (!ATOMIC_LOAD(&m_shutdown)) {
        // Wait for work or shutdown signal
        WaitForSingleObject(m_encodeEvent, 100);

        CaptureFrame captureFrame;
        while (m_captureQueue.Pop(captureFrame)) {
            if (!captureFrame.isValid) {
                continue;
            }

            DWORD encodeStartTime = GetTickCount();

            // Create encoded frame
            EncodedFrame encodedFrame;
            encodedFrame.frameId = captureFrame.frameId;
            encodedFrame.timestamp = captureFrame.timestamp;
            encodedFrame.header = captureFrame.header;
            memcpy(encodedFrame.regions, captureFrame.regions, sizeof(encodedFrame.regions));

            // Compress the region data
            if (captureFrame.regionDataSize > 0 && captureFrame.regionData) {
                DWORD workSpaceSize, fragmentWorkSpaceSize;
                Funcs::pRtlGetCompressionWorkSpaceSize(COMPRESSION_FORMAT_LZNT1,
                                                     &workSpaceSize, &fragmentWorkSpaceSize);

                BYTE* workSpace = (BYTE*)malloc(workSpaceSize);
                if (workSpace) {
                    encodedFrame.compressedData = (BYTE*)malloc(captureFrame.regionDataSize);
                    if (encodedFrame.compressedData) {
                        NTSTATUS status = Funcs::pRtlCompressBuffer(COMPRESSION_FORMAT_LZNT1,
                                                                  captureFrame.regionData,
                                                                  captureFrame.regionDataSize,
                                                                  encodedFrame.compressedData,
                                                                  captureFrame.regionDataSize,
                                                                  2048,
                                                                  &encodedFrame.compressedSize,
                                                                  workSpace);

                        if (status == 0) { // STATUS_SUCCESS
                            encodedFrame.isValid = TRUE;
                        } else {
                            // Compression failed, use uncompressed data
                            free(encodedFrame.compressedData);
                            encodedFrame.compressedData = (BYTE*)malloc(captureFrame.regionDataSize);
                            if (encodedFrame.compressedData) {
                                memcpy(encodedFrame.compressedData, captureFrame.regionData, 
                                      captureFrame.regionDataSize);
                                encodedFrame.compressedSize = captureFrame.regionDataSize;
                                encodedFrame.isValid = TRUE;
                            }
                        }
                    }
                    free(workSpace);
                }
            } else {
                encodedFrame.isValid = TRUE; // No data to compress
            }

            if (encodedFrame.isValid) {
                // Try to push to send queue
                if (!m_encodeQueue.Push(encodedFrame)) {
                    // Queue is full, drop this frame
                    ATOMIC_FETCH_ADD(&m_stats.droppedFrames, 1);
                    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning, 
                        "Dropped frame %d - encode queue full", encodedFrame.frameId);
                } else {
                    ATOMIC_FETCH_ADD(&m_stats.encodedFrames, 1);
                    SetEvent(m_sendEvent); // Signal send thread
                }
            }

            // Update timing statistics
            DWORD encodeTime = GetTickCount() - encodeStartTime;
            ATOMIC_STORE(&m_stats.avgEncodeTime,
                (ATOMIC_LOAD(&m_stats.avgEncodeTime) * 7 + encodeTime) / 8); // Moving average
        }
    }

    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Debug,
        "Encode thread finished");
}

void ThreadedCaptureSystem::SendThreadMain()
{
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Debug,
        "Send thread started");

    while (!ATOMIC_LOAD(&m_shutdown)) {
        // Wait for work or shutdown signal
        WaitForSingleObject(m_sendEvent, 100);

        EncodedFrame encodedFrame;
        while (m_encodeQueue.Pop(encodedFrame)) {
            if (!encodedFrame.isValid) {
                continue;
            }

            DWORD sendStartTime = GetTickCount();

            // Send frame data to server
            BOOL success = TRUE;

            // Send "has data" signal
            int hasData = 1;
            if (Funcs::pSend(m_socket, (char*)&hasData, sizeof(hasData), 0) <= 0) {
                success = FALSE;
            }

            if (success) {
                // Send differential capture header
                if (Funcs::pSend(m_socket, (char*)&encodedFrame.header, sizeof(encodedFrame.header), 0) <= 0) {
                    success = FALSE;
                }
            }

            if (success && encodedFrame.header.regionCount > 0) {
                // Send region information
                if (Funcs::pSend(m_socket, (char*)encodedFrame.regions,
                        sizeof(DirtyRegion) * encodedFrame.header.regionCount, 0) <= 0) {
                    success = FALSE;
                }
            }

            if (success && encodedFrame.compressedSize > 0) {
                // Send compressed data size
                if (Funcs::pSend(m_socket, (char*)&encodedFrame.compressedSize, sizeof(encodedFrame.compressedSize), 0) <= 0) {
                    success = FALSE;
                }

                // Send compressed data
                if (encodedFrame.compressedData) {
                    DWORD totalSent = 0;
                    while (totalSent < encodedFrame.compressedSize && success) {
                        int sent = Funcs::pSend(m_socket, (char*)encodedFrame.compressedData + totalSent,
                                               encodedFrame.compressedSize - totalSent, 0);
                        if (sent <= 0) {
                            success = FALSE;
                            break;
                        }
                        totalSent += sent;
                    }
                }
            } else if (success) {
                // No data to send
                DWORD noData = 0;
                if (Funcs::pSend(m_socket, (char*)&noData, sizeof(noData), 0) <= 0) {
                    success = FALSE;
                }
            }

            if (success) {
                // Wait for server response
                DWORD response;
                if (Funcs::pRecv(m_socket, (char*)&response, sizeof(response), 0) <= 0) {
                    success = FALSE;
                }
            }

            if (success) {
                ATOMIC_FETCH_ADD(&m_stats.sentFrames, 1);
                ATOMIC_FETCH_ADD(&m_stats.totalBandwidth, encodedFrame.compressedSize);
            } else {
                ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
                    "Failed to send frame %d", encodedFrame.frameId);
                // Connection error - should probably signal main thread to reconnect
                break;
            }

            // Update timing statistics
            DWORD sendTime = GetTickCount() - sendStartTime;
            ATOMIC_STORE(&m_stats.avgSendTime,
                (ATOMIC_LOAD(&m_stats.avgSendTime) * 7 + sendTime) / 8); // Moving average
        }
    }

    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Debug,
        "Send thread finished");
}

// Global C interface functions
BOOL InitializeThreadedCapture()
{
    if (g_captureSystem) {
        return TRUE; // Already initialized
    }

    g_captureSystem = new ThreadedCaptureSystem();
    if (!g_captureSystem) {
        return FALSE;
    }

    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
        "Threaded capture system initialized");
    return TRUE;
}

void CleanupThreadedCapture()
{
    if (g_captureSystem) {
        delete g_captureSystem;
        g_captureSystem = nullptr;
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
            "Threaded capture system cleaned up");
    }
}

BOOL StartThreadedCapture(SOCKET socket)
{
    if (!g_captureSystem) {
        return FALSE;
    }

    if (!g_captureSystem->Initialize(socket)) {
        return FALSE;
    }

    return g_captureSystem->Start();
}

void StopThreadedCapture()
{
    if (g_captureSystem) {
        g_captureSystem->Stop();
    }
}

ThreadedCaptureStats GetThreadedCaptureStats()
{
    if (g_captureSystem) {
        return g_captureSystem->GetStats();
    }
    return ThreadedCaptureStats{};
}
