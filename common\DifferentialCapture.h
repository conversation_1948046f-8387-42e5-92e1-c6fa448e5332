#pragma once
#include <Windows.h>

// Maximum number of regions that can be sent in a single frame
#define MAX_REGIONS_PER_FRAME 64

// Minimum region size to avoid sending tiny regions
#define MIN_REGION_SIZE 16

// Maximum distance between regions to merge them
#define REGION_MERGE_DISTANCE 32

// Structure to represent a changed region
struct DirtyRegion
{
    DWORD x;        // X coordinate of the region
    DWORD y;        // Y coordinate of the region  
    DWORD width;    // Width of the region
    DWORD height;   // Height of the region
    DWORD dataSize; // Size of pixel data for this region
};

// Header for differential capture data
struct DifferentialCaptureHeader
{
    DWORD frameWidth;       // Full frame width
    DWORD frameHeight;      // Full frame height
    DWORD screenWidth;      // Screen width
    DWORD screenHeight;     // Screen height
    DWORD regionCount;      // Number of regions in this frame
    DWORD totalDataSize;    // Total size of all region data
    BOOL isFullFrame;       // TRUE if this is a full frame (first frame or major change)
};

// Structure to hold region detection state
struct RegionDetectionState
{
    BYTE* previousFrame;    // Previous frame buffer
    BYTE* currentFrame;     // Current frame buffer
    DWORD frameWidth;       // Frame width
    DWORD frameHeight;      // Frame height
    DWORD bytesPerPixel;    // Bytes per pixel (3 for RGB)
    DWORD changeThreshold;  // Threshold for pixel change detection
    BOOL initialized;       // Whether the state is initialized
};

// Function declarations for differential capture
#ifdef __cplusplus
extern "C" {
#endif

// Initialize region detection state
BOOL InitializeRegionDetection(RegionDetectionState* state, DWORD width, DWORD height, DWORD bytesPerPixel);

// Cleanup region detection state
void CleanupRegionDetection(RegionDetectionState* state);

// Detect changed regions between current and previous frame
DWORD DetectChangedRegions(RegionDetectionState* state, BYTE* currentFrame, DirtyRegion* regions, DWORD maxRegions);

// Merge adjacent regions to reduce the number of regions
DWORD MergeAdjacentRegions(DirtyRegion* regions, DWORD regionCount, DWORD maxDistance);

// Extract pixel data for a specific region
BOOL ExtractRegionData(BYTE* frameBuffer, DWORD frameWidth, DWORD frameHeight, 
                      const DirtyRegion* region, BYTE* regionData);

// Apply region data to a frame buffer
BOOL ApplyRegionData(BYTE* frameBuffer, DWORD frameWidth, DWORD frameHeight,
                    const DirtyRegion* region, const BYTE* regionData);

// Adjust change threshold based on performance metrics
void AdjustChangeThreshold(RegionDetectionState* state, DWORD regionCount, DWORD targetRegionCount);

// Optimize regions by removing small isolated regions
DWORD OptimizeRegions(DirtyRegion* regions, DWORD regionCount, DWORD minRegionSize);

#ifdef __cplusplus
}
#endif
