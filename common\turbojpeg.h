/*
 * Minimal TurboJPEG header for HVNC integration
 * This is a simplified version for compilation purposes
 * Replace with the actual TurboJPEG library headers in production
 */

#ifndef __TURBOJPEG_H__
#define __TURBOJPEG_H__

#ifdef __cplusplus
extern "C" {
#endif

/* TurboJPEG API */
typedef void* tjhandle;

/* Pixel formats */
enum TJPF {
    TJPF_RGB = 0,
    TJPF_BGR,
    TJPF_RGBX,
    TJPF_BGRX,
    TJPF_XBGR,
    TJPF_XRGB,
    TJPF_GRAY,
    TJPF_RGBA,
    TJPF_BGRA,
    TJPF_ABGR,
    TJPF_ARGB,
    TJPF_CMYK
};

/* Chrominance subsampling options */
enum TJSAMP {
    TJSAMP_444 = 0,
    TJSAMP_422,
    TJSAMP_420,
    TJSAMP_GRAY,
    TJSAMP_440,
    TJSAMP_411
};

/* Flags */
#define TJFLAG_BOTTOMUP     2
#define TJFLAG_FASTUPSAMPLE 256
#define TJFLAG_FASTDCT      2048
#define TJFLAG_ACCURATEDCT  4096

/* Pixel size (in bytes) for a given pixel format */
extern const int tjPixelSize[12];

/* MCU block width (in pixels) for a given level of chrominance subsampling */
extern const int tjMCUWidth[6];

/* MCU block height (in pixels) for a given level of chrominance subsampling */
extern const int tjMCUHeight[6];

/* Function prototypes */
tjhandle tjInitCompress(void);
tjhandle tjInitDecompress(void);

unsigned long tjBufSize(int width, int height, int jpegSubsamp);

int tjCompress2(tjhandle handle, const unsigned char *srcBuf, int width,
                int pitch, int height, int pixelFormat, unsigned char **jpegBuf,
                unsigned long *jpegSize, int jpegSubsamp, int jpegQual,
                int flags);

int tjDecompress2(tjhandle handle, const unsigned char *jpegBuf,
                  unsigned long jpegSize, unsigned char *dstBuf, int width,
                  int pitch, int height, int pixelFormat, int flags);

int tjDestroy(tjhandle handle);

char* tjGetErrorStr(void);

void tjFree(unsigned char *buffer);

unsigned char *tjAlloc(int bytes);

/* Error codes */
#define TJERR_WARNING   0
#define TJERR_FATAL     1

#ifdef __cplusplus
}
#endif

#endif /* __TURBOJPEG_H__ */
