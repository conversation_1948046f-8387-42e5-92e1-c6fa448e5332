#pragma once
#include <memory>
#include <string>
#include <vector>
#include <functional>
#include "Common.h"

namespace ModernHVNC {

// Smart pointer type aliases with custom deleters
template<typename T>
using unique_local_ptr = std::unique_ptr<T, decltype([](T* p) { if (p) LocalFree(p); })>;

template<typename T>
using unique_malloc_ptr = std::unique_ptr<T, decltype([](T* p) { if (p) free(p); })>;

template<typename T, typename Deleter>
using unique_funcs_ptr = std::unique_ptr<T, Deleter>;

// RAII wrapper for Windows handles
class HandleWrapper {
private:
    HANDLE handle_;
    
public:
    explicit HandleWrapper(HANDLE h = INVALID_HANDLE_VALUE) noexcept : handle_(h) {}
    
    ~HandleWrapper() noexcept {
        if (handle_ != INVALID_HANDLE_VALUE && handle_ != nullptr) {
            CloseHandle(handle_);
        }
    }
    
    // Move constructor and assignment
    HandleWrapper(HandleWrapper&& other) noexcept : handle_(other.handle_) {
        other.handle_ = INVALID_HANDLE_VALUE;
    }
    
    HandleWrapper& operator=(HandleWrapper&& other) noexcept {
        if (this != &other) {
            if (handle_ != INVALID_HANDLE_VALUE && handle_ != nullptr) {
                CloseHandle(handle_);
            }
            handle_ = other.handle_;
            other.handle_ = INVALID_HANDLE_VALUE;
        }
        return *this;
    }
    
    // Delete copy constructor and assignment
    HandleWrapper(const HandleWrapper&) = delete;
    HandleWrapper& operator=(const HandleWrapper&) = delete;
    
    HANDLE get() const noexcept { return handle_; }
    HANDLE release() noexcept {
        HANDLE temp = handle_;
        handle_ = INVALID_HANDLE_VALUE;
        return temp;
    }
    
    explicit operator bool() const noexcept { 
        return handle_ != INVALID_HANDLE_VALUE && handle_ != nullptr; 
    }
};

// RAII wrapper for registry keys
class RegistryKeyWrapper {
private:
    HKEY key_;
    
public:
    explicit RegistryKeyWrapper(HKEY k = nullptr) noexcept : key_(k) {}
    
    ~RegistryKeyWrapper() noexcept {
        if (key_) {
            RegCloseKey(key_);
        }
    }
    
    // Move constructor and assignment
    RegistryKeyWrapper(RegistryKeyWrapper&& other) noexcept : key_(other.key_) {
        other.key_ = nullptr;
    }
    
    RegistryKeyWrapper& operator=(RegistryKeyWrapper&& other) noexcept {
        if (this != &other) {
            if (key_) {
                RegCloseKey(key_);
            }
            key_ = other.key_;
            other.key_ = nullptr;
        }
        return *this;
    }
    
    // Delete copy constructor and assignment
    RegistryKeyWrapper(const RegistryKeyWrapper&) = delete;
    RegistryKeyWrapper& operator=(const RegistryKeyWrapper&) = delete;
    
    HKEY get() const noexcept { return key_; }
    HKEY release() noexcept {
        HKEY temp = key_;
        key_ = nullptr;
        return temp;
    }
    
    explicit operator bool() const noexcept { return key_ != nullptr; }
};

// Modern string utilities
std::string Utf16toUtf8String(const wchar_t* utf16);
std::wstring Utf8toUtf16String(const char* utf8);
std::string FormatString(const char* format, ...);
std::wstring FormatWString(const wchar_t* format, ...);

// Structured binding utilities
std::pair<DWORD, DWORD> GetFileSize(HANDLE hFile) noexcept;
std::tuple<bool, DWORD, DWORD> GetWindowDimensions(HWND hWnd) noexcept;

// Helper functions for smart pointer creation
template<typename T>
auto make_local_unique(T* ptr) {
    return unique_local_ptr<T>(ptr);
}

template<typename T, typename Deleter>
auto make_funcs_unique(T* ptr, Deleter deleter) {
    return unique_funcs_ptr<T, Deleter>(ptr, deleter);
}

} // namespace ModernHVNC
