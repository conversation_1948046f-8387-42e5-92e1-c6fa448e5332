#pragma once
#include "DifferentialCapture.h"
#include <Windows.h>

// Use Windows Interlocked functions instead of std::atomic for better compatibility
#define ATOMIC_LOAD(ptr) InterlockedCompareExchange((LONG volatile*)(ptr), 0, 0)
#define ATOMIC_STORE(ptr, val) InterlockedExchange((LONG volatile*)(ptr), (LONG)(val))
#define ATOMIC_FETCH_ADD(ptr, val) InterlockedExchangeAdd((LONG volatile*)(ptr), (LONG)(val))
#define ATOMIC_COMPARE_EXCHANGE(ptr, expected, desired) \
    (InterlockedCompareExchange((LONG volatile*)(ptr), (LONG)(desired), (LONG)(expected)) == (LONG)(expected))

// Forward declarations
struct CaptureFrame;
struct EncodedFrame;

// Maximum number of frames in each queue
#define MAX_QUEUE_SIZE 8

// Thread-safe lockless queue implementation using atomic operations
template<typename T, size_t Size>
class LocklessQueue
{
private:
    __declspec(align(64)) volatile LONG m_head;
    __declspec(align(64)) volatile LONG m_tail;
    __declspec(align(64)) T m_data[Size];

public:
    LocklessQueue() : m_head(0), m_tail(0) {}
    ~LocklessQueue() {}

    // Non-copyable
    LocklessQueue(const LocklessQueue&);
    LocklessQueue& operator=(const LocklessQueue&);

    bool Push(const T& item)
    {
        const LONG current_tail = ATOMIC_LOAD(&m_tail);
        const LONG next_tail = (current_tail + 1) % Size;

        if (next_tail == ATOMIC_LOAD(&m_head)) {
            return false; // Queue is full
        }

        m_data[current_tail] = item;
        ATOMIC_STORE(&m_tail, next_tail);
        return true;
    }

    bool Pop(T& item)
    {
        const LONG current_head = ATOMIC_LOAD(&m_head);

        if (current_head == ATOMIC_LOAD(&m_tail)) {
            return false; // Queue is empty
        }

        item = m_data[current_head];
        ATOMIC_STORE(&m_head, (current_head + 1) % Size);
        return true;
    }

    bool IsEmpty() const
    {
        return ATOMIC_LOAD(&m_head) == ATOMIC_LOAD(&m_tail);
    }

    bool IsFull() const
    {
        const LONG current_tail = ATOMIC_LOAD(&m_tail);
        const LONG next_tail = (current_tail + 1) % Size;
        return next_tail == ATOMIC_LOAD(&m_head);
    }

    DWORD GetSize() const
    {
        const LONG head = ATOMIC_LOAD(&m_head);
        const LONG tail = ATOMIC_LOAD(&m_tail);
        return (tail >= head) ? (tail - head) : (Size - head + tail);
    }
};

// Structure to hold captured frame data
struct CaptureFrame
{
    DWORD frameId;
    DWORD timestamp;
    DifferentialCaptureHeader header;
    DirtyRegion regions[MAX_REGIONS_PER_FRAME];
    BYTE* regionData;
    DWORD regionDataSize;
    BOOL isValid;

    CaptureFrame() : frameId(0), timestamp(0), regionData(nullptr), regionDataSize(0), isValid(FALSE) {}
    
    ~CaptureFrame()
    {
        if (regionData) {
            free(regionData);
            regionData = nullptr;
        }
    }

    // Copy constructor (needed for queue operations)
    CaptureFrame(const CaptureFrame& other)
        : frameId(other.frameId)
        , timestamp(other.timestamp)
        , header(other.header)
        , regionDataSize(other.regionDataSize)
        , isValid(other.isValid)
    {
        memcpy(regions, other.regions, sizeof(regions));
        if (other.regionData && other.regionDataSize > 0) {
            regionData = (BYTE*)malloc(other.regionDataSize);
            if (regionData) {
                memcpy(regionData, other.regionData, other.regionDataSize);
            } else {
                regionDataSize = 0;
                isValid = FALSE;
            }
        } else {
            regionData = nullptr;
        }
    }

    // Assignment operator
    CaptureFrame& operator=(const CaptureFrame& other)
    {
        if (this != &other) {
            if (regionData) {
                free(regionData);
                regionData = nullptr;
            }

            frameId = other.frameId;
            timestamp = other.timestamp;
            header = other.header;
            memcpy(regions, other.regions, sizeof(regions));
            regionDataSize = other.regionDataSize;
            isValid = other.isValid;

            if (other.regionData && other.regionDataSize > 0) {
                regionData = (BYTE*)malloc(other.regionDataSize);
                if (regionData) {
                    memcpy(regionData, other.regionData, other.regionDataSize);
                } else {
                    regionDataSize = 0;
                    isValid = FALSE;
                }
            }
        }
        return *this;
    }
};

// Structure to hold encoded frame data ready for transmission
struct EncodedFrame
{
    DWORD frameId;
    DWORD timestamp;
    DifferentialCaptureHeader header;
    DirtyRegion regions[MAX_REGIONS_PER_FRAME];
    BYTE* compressedData;
    DWORD compressedSize;
    BOOL isValid;

    EncodedFrame() : frameId(0), timestamp(0), compressedData(nullptr), compressedSize(0), isValid(FALSE) {}
    
    ~EncodedFrame()
    {
        if (compressedData) {
            free(compressedData);
            compressedData = nullptr;
        }
    }

    // Copy constructor (needed for queue operations)
    EncodedFrame(const EncodedFrame& other)
        : frameId(other.frameId)
        , timestamp(other.timestamp)
        , header(other.header)
        , compressedSize(other.compressedSize)
        , isValid(other.isValid)
    {
        memcpy(regions, other.regions, sizeof(regions));
        if (other.compressedData && other.compressedSize > 0) {
            compressedData = (BYTE*)malloc(other.compressedSize);
            if (compressedData) {
                memcpy(compressedData, other.compressedData, other.compressedSize);
            } else {
                compressedSize = 0;
                isValid = FALSE;
            }
        } else {
            compressedData = nullptr;
        }
    }

    // Assignment operator
    EncodedFrame& operator=(const EncodedFrame& other)
    {
        if (this != &other) {
            if (compressedData) {
                free(compressedData);
                compressedData = nullptr;
            }

            frameId = other.frameId;
            timestamp = other.timestamp;
            header = other.header;
            memcpy(regions, other.regions, sizeof(regions));
            compressedSize = other.compressedSize;
            isValid = other.isValid;

            if (other.compressedData && other.compressedSize > 0) {
                compressedData = (BYTE*)malloc(other.compressedSize);
                if (compressedData) {
                    memcpy(compressedData, other.compressedData, other.compressedSize);
                } else {
                    compressedSize = 0;
                    isValid = FALSE;
                }
            }
        }
        return *this;
    }
};

// Thread-safe statistics structure
struct ThreadedCaptureStats
{
    __declspec(align(64)) volatile LONG capturedFrames;
    __declspec(align(64)) volatile LONG encodedFrames;
    __declspec(align(64)) volatile LONG sentFrames;
    __declspec(align(64)) volatile LONG droppedFrames;
    __declspec(align(64)) volatile LONG avgCaptureTime;
    __declspec(align(64)) volatile LONG avgEncodeTime;
    __declspec(align(64)) volatile LONG avgSendTime;
    __declspec(align(64)) volatile LONG totalBandwidth;

    ThreadedCaptureStats()
        : capturedFrames(0), encodedFrames(0), sentFrames(0), droppedFrames(0)
        , avgCaptureTime(0), avgEncodeTime(0), avgSendTime(0), totalBandwidth(0) {}
};

// Main threaded capture system
class ThreadedCaptureSystem
{
private:
    // Thread handles
    HANDLE m_captureThread;
    HANDLE m_encodeThread;
    HANDLE m_sendThread;

    // Thread control
    volatile LONG m_running;
    volatile LONG m_shutdown;

    // Queues for inter-thread communication
    LocklessQueue<CaptureFrame, MAX_QUEUE_SIZE> m_captureQueue;
    LocklessQueue<EncodedFrame, MAX_QUEUE_SIZE> m_encodeQueue;

    // Thread synchronization events
    HANDLE m_captureEvent;
    HANDLE m_encodeEvent;
    HANDLE m_sendEvent;

    // Connection socket
    SOCKET m_socket;

    // Frame counter
    volatile LONG m_frameCounter;

    // Statistics
    ThreadedCaptureStats m_stats;

    // Thread functions
    static DWORD WINAPI CaptureThreadProc(LPVOID param);
    static DWORD WINAPI EncodeThreadProc(LPVOID param);
    static DWORD WINAPI SendThreadProc(LPVOID param);

    // Internal methods
    void CaptureThreadMain();
    void EncodeThreadMain();
    void SendThreadMain();

public:
    ThreadedCaptureSystem() : m_captureThread(nullptr), m_encodeThread(nullptr), m_sendThread(nullptr)
        , m_running(0), m_shutdown(0), m_captureEvent(nullptr), m_encodeEvent(nullptr)
        , m_sendEvent(nullptr), m_socket(INVALID_SOCKET), m_frameCounter(0) {}
    ~ThreadedCaptureSystem();

    // Initialize the system
    BOOL Initialize(SOCKET socket);

    // Start all threads
    BOOL Start();

    // Stop all threads
    void Stop();

    // Get statistics
    ThreadedCaptureStats GetStats() const;

    // Check if system is running
    bool IsRunning() const { return ATOMIC_LOAD(&m_running) != 0; }
};

// Global functions
#ifdef __cplusplus
extern "C" {
#endif

// Initialize threaded capture system
BOOL InitializeThreadedCapture();

// Cleanup threaded capture system
void CleanupThreadedCapture();

// Start threaded capture with socket
BOOL StartThreadedCapture(SOCKET socket);

// Stop threaded capture
void StopThreadedCapture();

#ifdef __cplusplus
}
#endif

// C++ only functions (not C linkage)
#ifdef __cplusplus
// Get capture statistics
ThreadedCaptureStats GetThreadedCaptureStats();
#endif
